APP_NAME="SFG Onboarding"
APP_ENV=local
APP_KEY=base64:AvLtActf5/h0WoSRMn6WFcZeY5tRj/zgaXuPiWw/zqA=
APP_DEBUG=true
APP_URL=https://quilityonboarding.test/
APP_TENANT=Q2A
MIX_APP_TENANT="${APP_TENANT}"

#B2B AGENT TENANT ID
APP_TENANT_ID=124

#Q2A
MIX_APP_PRIMARY_COLOR_LIGHT="#46C3B2"
MIX_APP_SECONDARY_COLOR_DARK="#005851"

AVAILABLE_CARRIERS="American Amicable & Occidental,American General (AIG),Americo,Assurity,F&G,Foresters,Gerber,Global Atlantic,Great Western,John Hancock,Lafayette Life,Liberty Bankers,Lincoln Financial,Manhattan Life,Mutual of Omaha (MOO),Mutual Trust,National Life Group (NLG & LSW),Prosperity Life,Royal Neighbors,Transamerica"
#AVAILABLE_CARRIERS="Aetna,Allianz,American Amicable,Americo,ANICO,Assurity,Baltimore Life,Banner,Columbus Life,Corebridge Life (AIG),F&G,Foresters,John Hancock Brokerage/Traditional,John Hancock Simple Term,Lafayette Life,Legal & General (LGA),Liberty Bankers,Lincoln Financial,Midland,Mutual of Omaha,Mutual Trust,National Life Group (LSW),Nationwide,North American Life,PacLife Promise,Principal,Protective,Prudential,Royal Neighbors,SBLI,Securian-Minn Life,Transamerica,United Home Life"
MIX_AVAILABLE_CARRIERS="${AVAILABLE_CARRIERS}"

#Q2B
# MIX_APP_PRIMARY_COLOR_LIGHT="#ff0000"
# MIX_APP_SECONDARY_COLOR_DARK="#0083EB"


# APP_TENANT_LOGO=/images/onboarding-portal-stacked.png
# APP_TENANT_LOGO=/images/QuilityLogoHorizontalBlackRGB-51147b7f.svg
# APP_TENANT_LOGO=/images/Quility-B2B-Logo-Stacked-Blue.svg
# MIX_APP_TENANT_LOGO="${APP_TENANT_LOGO}"

# APP_TENANT_FOOTER_LOGO=images/emails/onboarding-portal.png
# APP_TENANT_FOOTER_LOGO=images/QuilityLogoHorizontalBlackRGB-51147b7f.svg
# APP_TENANT_FOOTER_LOGO=images/Quility-B2B-Logo-Stacked-Blue.svg
# MIX_APP_TENANT_FOOTER_LOGO="${APP_TENANT_FOOTER_LOGO}"

APP_COPYRIGHT_TEXT="Copyright© 2024"
MIX_APP_COPYRIGHT_TEXT="${APP_COPYRIGHT_TEXT}"

MIX_APP_AUTH_BACKGROUND_IMAGE=/images/emails/onboarding-bg.jpg

APP_PRINCIPAL_UPLINE_AGENT_CODE=QBB0048665
MIX_APP_PRINCIPAL_UPLINE_AGENT_CODE="${APP_PRINCIPAL_UPLINE_AGENT_CODE}"

# AGENT_DOCUMENT_TEMPLATE_ID=3e6836e66081dc3c9cac2def64ea0f65e59d57e0
# PRINCIPAL_STANDARD_AGREEMENT_TEMPLATE_ID=addfdbbe28bbe733b34295aefd3756589ba2ed76
# PRINCIPAL_CUSTOM_AGREEMENT_TEMPLATE_ID=782381bb200c688d6f0450663c7b99bd39ebc9c9
LOG_CHANNEL=stack

# #local db
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=quility_onboarding_portal
# DB_USERNAME=root
# DB_PASSWORD=


###################################################################################
# LOCAL DEV DB SETTINGS
###################################################################################
#remote dev/staging db
#ssh forge@************ -L 1433:**********:1433 -N
DB_CONNECTION=sqlsrv-internal
DB_HOST=**********
DB_PORT=1433
DB_DATABASE=sfgonboarding_STAGING
# DB_DATABASE=sfgonboarding
DB_USERNAME=sfgobp2
DB_PASSWORD="bL*qc8#1ZG&LSdK5"

# DEV 
MSSQL_HOST=**********
MSSQL_DATABASE=HQ_QLT_DEV
MSSQL_USERNAME="np_au_quility_hq"
MSSQL_PASSWORD="^ulu*k&Y1BSiD25tf0&BcX63"
MSSQL_PORT=1433

# DEV - B2B
MSSQL_B2B_HOST=**********
MSSQL_B2B_DATABASE=HQ_B2B_DEV
MSSQL_B2B_USERNAME="np_au_b2b_hq"
MSSQL_B2B_PASSWORD="&j#TV7Yay3Atgr@7!0h*708O"
MSSQL_B2B_PORT=1433

# HQ MySQL
HQ_MYSQL_CONNECTION=mysql
HQ_MYSQL_HOST=***********
HQ_MYSQL_PORT=3306
HQ_MYSQL_DATABASE=staging
HQ_MYSQL_USERNAME=staging_user
HQ_MYSQL_PASSWORD=756ypuc7ms9fub10osk5
###################################################################################


# ###################################################################################
# # PROD DB SETTINGS
# ###################################################################################
# DB_CONNECTION=sqlsrv-internal
# DB_HOST=127.0.0.1
# DB_PORT=1433
# DB_DATABASE=sfgonboarding
# # DB_DATABASE=sfgonboarding
# DB_USERNAME=sfgobp2
# DB_PASSWORD="bL*qc8#1ZG&LSdK5"

# # DEV 
# MSSQL_HOST=127.0.0.1
# MSSQL_DATABASE=HQ_QLT
# MSSQL_USERNAME="au_obp_hq"
# MSSQL_PASSWORD="SmfPnUxbWUuIvTNm4792"
# MSSQL_PORT=1435

# # DEV - B2B
# MSSQL_B2B_HOST=127.0.0.1
# MSSQL_B2B_DATABASE=HQ_B2B
# MSSQL_B2B_USERNAME="au_b2b_hq"
# MSSQL_B2B_PASSWORD="wRQXeqreph3ZYQPO0X7k"
# MSSQL_B2B_PORT=1436
# ###################################################################################

# ####### these settings work locally
# BROADCAST_DRIVER=log
# CACHE_DRIVER=file
# QUEUE_CONNECTION=sync

# SESSION_DRIVER=file
# SESSION_LIFETIME=120
# SESSION_DOMAIN=.quilityonboarding.test
# SANCTUM_STATEFUL_DOMAINS=quilityonboarding.test
# ######### end local settings


##################### start redis test
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
QUEUE_CONNECTION=sync
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=""
REDIS_PORT=6379
REDIS_CLIENT=predis
##################### end redis test

# MAIL_DRIVER=mandrill
# MAIL_MAILER=mandrill
# MAIL_HOST=smtp.mandrillapp.com
# MAIL_PORT=587 
# MAIL_USERNAME="SFG - Agents"
# MAIL_PASSWORD=**********************
# MAIL_ENCRYPTION=null
MAIL_MAILER=logged
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
# MAIL_USERNAME=1e1721b4fadc84
# MAIL_PASSWORD=4fa16f69bc006d
MAIL_USERNAME=838750675b1299
MAIL_PASSWORD=e002a4b701a4c3
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Symmetry Financial Group"

MANDRILL_KEY=**********************

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=caa0f6948f2a3f615
PUSHER_APP_KEY=b141571c6060dea
PUSHER_APP_SECRET=9f9cc07c29926da29
PUSHER_APP_CLUSTER=mt303

MIX_PUSHER_APP_KEY=""
MIX_PUSHER_APP_CLUSTER=""

AUTH0_DOMAIN=https://test-laravel.auth0.com
# AUTH0_DOMAIN=test-laravel.auth0.com
# AUTH0_DOMAIN=https://auth-dev.quility.com
# AUTH0_DOMAIN=https://localhost:8080/api
AUTH0_CLIENT_ID=VjSG0DoQzIShatuve7eRIg8aJLUMDLJL
AUTH0_CLIENT_SECRET=****************************************************************
AUTH0_AUDIENCE=http://localhost:8080/api
AUTH0_GUARD=auth0-session


QUILITYACCOUNT_CLIENT_ID=VxsMKypE9cPFS7kjQxf5tjw9aEzrLDx6
QUILITYACCOUNT_CLIENT_SECRET=****************************************************************

AZURE_STORAGE_NAME=agentdocuments
AZURE_STORAGE_KEY=ZjuorUvlSiY25W5oaXtRMnbi1Llibeh6ovYDUmpy/31B4izfkhjh+pPGoOjQ0V/4ksz7esbvR9Ng0kyhhJLQiQ==
AZURE_STORAGE_CONTAINER=dev

SFG_EXTERNAL_AUTH_KEY=9FQu4c3CjCNW2Vr9C3axtMGKbsDqy3nHSFD
#dev
# SFG_INTERNAL_SECURE_KEY=def00000650a342deec31151fdf934796bf4b4b096927fbdffe72f02db25a43b84cdc067475b72e4b81e8b1d8ce39d53754921e8fe0e2c9e4e330ada18b4deca2139036e
#prod
SFG_INTERNAL_SECURE_KEY=def000009d2695c0d3e56e083571b7baac9a2113e9b775755efa9bb4a5efd44f2b6f3757e677cde53314bc1df7de2c06f91040531a94f3a9305ec559e1a0c6ff98f15f16


VUE_APP_API_FILE_UPLOAD_URL=
VUE_APP_API_FILE_UPLOAD_CHUNKSIZE=

VUE_APP_TINYMCE_APIKEY=ta9eod9htwsc7tbqm2y5jwd74f79auw1kcq945gb2elwgp6v
MIX_TINYMCE_APIKEY=9srvj0c4xt4yzih63zn5dcbkajl91ywqvvesqlww0qomkm6g

#VUE_APP_BASE_URL=https://dev.quilityonboarding.com
VUE_APP_BASE_URL=https://quilityonboarding.test

MIX_SHOW_LOGIN_SLIDER=true
MIX_LOGIN_SLIDER_AGENT_PAGE_ID=127
MIX_LOGIN_SLIDER_RECRUIT_PAGE_ID=607

# LARAVEL_WEBSOCKETS_PATH=wss://dev.quilityonboarding.com/socket
LARAVEL_WEBSOCKETS_PATH=wss://quilityonboarding.test:6001/socket
MIX_LARAVEL_WEBSOCKETS_PATH="${LARAVEL_WEBSOCKETS_PATH}"
MIX_APP_NAME="${APP_NAME}"
MIX_APP_ENV="${APP_ENV}"
MIX_API_BASE_URL="${APP_URL}"
MIX_API_FILE_UPLOAD_URL="${APP_URL}api/file/upload"
MIX_FILE_UPLOAD_CHUNKSIZE=1024*1024*2
MIX_PREVIEW_APPLICATION_FORM=false
MIX_FRONTEND_BASE_URL=https://quilityonboarding.test

SUPER_ADMIN_PASSWORD=SHzUudCprHOAwg7E0jkz

SYSTEM_SECURE_PLACEHOLDER="secure-placeholder"
MIX_SYSTEM_SECURE_PLACEHOLDER="${SYSTEM_SECURE_PLACEHOLDER}"

# HELLOSIGN_CLIENT_ID=fd293f881f78cced8ffdb62d99049926
# HELLOSIGN_API_KEY=****************************************************************
HELLOSIGN_CLIENT_ID=7ea6b0abe86550cdbc875c822c8ead57
HELLOSIGN_API_KEY=****************************************************************

MIX_HELLOSIGN_SKIP_DOMAIN_VERIFICATION=true

HQ_ACCOUNT_AUTH_PATH="https://test-laravel.auth0.com/oauth/token"
HQ_ACCOUNT_CREATION_PATH="https://dev-dashboard.quility.com/api/"
# HQ_ACCOUNT_CREATION_PATH="http://localhost:8080/api/"
# HQ_ACCOUNT_CREATION_PATH="https://stg-hq.quility.com/api/"
HQ_ACCOUNT_AUDIENCE_PATH="http://localhost:8080/api"

# AGENTSYNC_CLIENT_KEY=3MVG9oZtFCVWuSwOQH9MIq.qbTzTZzn1qM2kbzzKe5IkgOzOZkSAu6RAUX7AWUsuAl.C_gVRg_q8jWKj0LgIg
# AGENTSYNC_CLIENT_SECRET=****************************************************************
# AGENTSYNC_API_URL=https://quility-agentsync--agentsync1.my.salesforce.com
# AGENTSYNC_USERNAME=<EMAIL>.agentsync1
# AGENTSYNC_PASSWORD=ZQN6btc3yvk*vkw*dct
# AGENTSYNC_SECURITY_TOKEN=OT3bxVVFuvtKAvZ9n3szsgv5q

# AgentSync API - copied over from HQ
AGENTSYNC_USE_PROD=false
AGENTSYNC_DEV_KEY=2vdl3lbii7u4lukm0iea9b4610
AGENTSYNC_DEV_SECRET=155tmg3ou46slrumrqpkmk83nsb7utc4mifok0ust34ubvtfiutg
AGENTSYNC_PROD_KEY=691qjrmrgnof5c3svt20567p31
AGENTSYNC_PROD_SECRET=qjmb22o10j8p8nm19vltnpvkjoaoitk33tn3jab82e7msg0eic3


UPLOAD_WEBP_CONVERT=false


AUTH0_HQ_DOMAIN=auth-dev.quility.com
AUTH0_HQ_DOMAIN2=test-laravel.auth0.com
AUTH0_HQ_CLIENT_ID=PEgDldJ3pC9u4pI5FZhkGPZZN3qjHEcP
AUTH0_HQ_CLIENT_SECRET=****************************************************************
AUTH0_HQ_CLIENT_ID_OLD=mwBlwvZ2nAgdK1sagZ44g9Z5mz2Vyi1p
AUTH0_HQ_CLIENT_SECRET_OLD=****************************************************************
AUTH0_HQ_API_IDENTIFIER=http://localhost:8080/api
AUTH0_HQ_AUTHORIZED_ISS=https://auth-dev.quility.com/
AUTH0_HQ_AUTHORIZED_ISS_2=https://test-laravel.auth0.com/
AUTH0_HQ_NAMESPACE=http://quility.com/
AUTH0_HQ_AUDIENCE=http://localhost:8080/api
AUTH0_HQ_STRATEGY=api
# AUTH0_HQ_MANAGEMENT_CLIENT_ID=PEgDldJ3pC9u4pI5FZhkGPZZN3qjHEcP
# AUTH0_HQ_MANAGEMENT_CLIENT_SECRET=****************************************************************
# AUTH0_HQ_MANAGEMENT_AUDIENCE=https://test-laravel.auth0.com/api/v2/
# AUTH0_HQ_MANAGEMENT_AUDIENCE=http://localhost:8080/api
# AUTH0_HQ_MANAGEMENT_STRATEGY=api


# demo portal settings
DEMO_PORTAL_BASE_URL=https://demo.quilityonboarding.com
DEMO_PORTAL_AUTH0_DOMAIN=auth-dev.quility.com
DEMO_PORTAL_AUTH0_CLIENT_ID=PEgDldJ3pC9u4pI5FZhkGPZZN3qjHEcP
DEMO_PORTAL_AUTH0_CLIENT_SECRET=****************************************************************
DEMO_PORTAL_AUTH0_AUDIENCE=http://localhost:8080/api


#NIPR NPN lookup service
# NIPR_BASE_URL=https://npn-ws.api.beta.nipr.com
# NIPR_WSDL_URL=https://npn-ws.api.beta.nipr.com/npn-ws/npnLookup?wsdl
# NIPR_USERNAME=beta83symtry
# NIPR_PASSWORD=taurus1973!nipr

NIPR_BASE_URL=https://npn-ws.api.nipr.com
NIPR_WSDL_URL=https://npn-ws.api.nipr.com/npn-ws/npnLookup?wsdl
NIPR_USERNAME=apiquility2
# NIPR_PASSWORD=O%ybV9696E3X
# NIPR_PASSWORD=MGN73WgRkEn_$rt
# NIPR_PASSWORD=WxWM5Es_vLL00641
# NIPR_PASSWORD="Y65sAsmj#hg6GNS"
# NIPR_PASSWORD="mj#hg6GNSY65sAs1"
# NIPR_PASSWORD="%8E3Cc6?^zXh!@#123"
# NIPR_PASSWORD="vPTe6s!0b756loT3kqR6XMk"
# NIPR_PASSWORD="!S2pDirDe+V4upc"
NIPR_PASSWORD="!S2pDirDe+V4upc1"


DAILY_REPORT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>,<EMAIL>
GROUP_ONBOARDING_REQUEST_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

SURELC_ENABLED=true
SURELC_NO_EMAILS=true
SURELC_API_KEY=DvEVZKuJxbDD9Q7cXBMCM68oCQgVA1QisP6nsgIoWO4lgYl5
SURELC_BASE_URL=https://surelc.surancebay.com/api/v2

# Auth0 Management API Credentials for retrieving user metadata
AUTH0_MANAGEMENT_API_CLIENT_ID=PEgDldJ3pC9u4pI5FZhkGPZZN3qjHEcP
AUTH0_MANAGEMENT_API_CLIENT_SECRET=****************************************************************