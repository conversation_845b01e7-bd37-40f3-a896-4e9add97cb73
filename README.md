# Insurance Agent Onboarding Portal

A comprehensive Laravel-based insurance agent onboarding system with dual-tenant support for Q2A (quilityonboarding.com) and Q2B (goliveb2b.com) versions. This application streamlines the agent recruitment, application, and contracting process with seamless integration to HQ systems.

## Table of Contents

- [Application Overview](#markdown-header-application-overview)
- [Architecture & Technology Stack](#markdown-header-architecture-technology-stack)
- [System Integration Details](#markdown-header-system-integration-details)
- [Environment Configuration](#markdown-header-environment-configuration)
- [Developer Setup Guide](#markdown-header-developer-setup-guide)
- [Database Schema Overview](#markdown-header-database-schema-overview)
- [API Documentation](#markdown-header-api-documentation)
- [Application Content Management](#markdown-header-application-content-management)
- [Development Workflow](#markdown-header-development-workflow)
- [Testing](#markdown-header-testing)
- [Deployment](#markdown-header-deployment)
- [Support and Documentation](#markdown-header-support-and-documentation)

## Application Overview

### Business Purpose
This system manages the complete insurance agent onboarding workflow:
1. **Agent Invitation**: Existing agents invite new candidates
2. **Application Process**: Multi-step form completion with document uploads
3. **Review & Approval**: Agency owner and home office review process
4. **Contract Execution**: Digital signature via HelloSign integration
5. **HQ Integration**: Automatic agent creation and synchronization with main systems

### Dual-Tenant Architecture
The application supports two distinct versions controlled via environment variables:

#### Q2A Version (quilityonboarding.com)
- **Target**: Quility/Symmetry Financial Group agents
- **Tenant ID**: Configurable via `APP_TENANT=Q2A`
- **Database**: Connects to Q2A HQ system
- **Branding**: Quility color scheme and branding

#### Q2B Version (goliveb2b.com)
- **Target**: B2B partner agents
- **Tenant ID**: Configurable via `APP_TENANT=Q2B`
- **Database**: Connects to B2B HQ system
- **Branding**: B2B-specific color scheme and branding
- **Features**: Additional B2B-specific application questions and contract types

### Key Features
- Multi-step application forms with conditional logic
- Document upload and management via Azure Storage
- Digital signature integration with HelloSign
- Real-time WebSocket notifications
- Agent hierarchy and permission management
- Automated HQ system synchronization
- NIPR NPN verification and license validation
- Multi-language support preparation

## Architecture & Technology Stack

### Backend Stack
- **Framework**: Laravel 9.x
- **PHP Version**: 8.1+
- **Database**: Microsoft SQL Server (multiple connections)
  - Primary: Application data (MySQL/SQL Server)
  - HQ: Q2A production data (SQL Server)
  - B2B HQ: Q2B production data (SQL Server)
- **Queue System**: Redis with Laravel Horizon
- **Cache**: Redis
- **Session Storage**: Database/Redis
- **File Storage**: Azure Blob Storage

### Frontend Stack
- **Framework**: Vue.js 2.6
- **UI Library**: Vuetify 2.4
- **State Management**: Vuex with persistence
- **Build Tool**: Laravel Mix with Webpack
- **Real-time**: Laravel WebSockets
- **Additional Libraries**:
  - Vue Router for SPA routing
  - Axios for HTTP requests
  - HelloSign Embedded for digital signatures
  - Vue Signature Pad for signature capture

### Authentication & Authorization
- **Primary Auth**: Auth0 integration with JWT tokens
- **Multi-tenant**: Tenant-specific Auth0 configurations
- **Permissions**: Laravel Spatie Permissions with role-based access
- **Impersonation**: Laravel Impersonate for admin functionality

### External Integrations
- **HQ Systems**: RESTful API integration for agent data synchronization
- **NIPR API**: NPN verification and license validation via SOAP web services
- **HelloSign**: Digital contract execution
- **Azure Storage**: Document and file management
- **Mandrill**: Email delivery service
- **Auth0**: Authentication and user management

## System Integration Details

### HQ System Communication

The application integrates with two separate HQ systems based on tenant configuration:

#### Q2A HQ Integration
```php
// Environment Configuration
MSSQL_HOST=**********
MSSQL_DATABASE=HQ_QLT_DEV
MSSQL_USERNAME=np_au_quility_hq
MSSQL_PASSWORD=your_password_here
```

#### B2B HQ Integration
```php
// Environment Configuration
MSSQL_B2B_HOST=**********
MSSQL_B2B_DATABASE=HQ_B2B_DEV
MSSQL_B2B_USERNAME=np_au_b2b_hq
MSSQL_B2B_PASSWORD=your_password_here
```

### Agent Synchronization Process

When an agent completes the onboarding process, the system automatically:

1. **Creates HQ Account**: `CreateHQAccount` job processes the agent data
2. **Generates Agent Code**: Unique identifier assigned by HQ system
3. **Uploads Documents**: All application documents transferred to HQ
4. **Sets Permissions**: Role-based access configured in HQ
5. **Carrier Configuration**: Selected carriers stored as agent config (B2B only)

#### Key Integration Classes
- `HQAccountServiceProvider`: Main HQ API communication
- `CreateHQAccount`: Job for agent creation
- `NiprSoapService`: NPN verification via NIPR SOAP API
- `UserRemoteAccount`: Model for HQ account tracking

### API Endpoints for HQ Integration

#### Agent Creation
```php
POST /trusted/base_agent
// Creates new agent in HQ system
// Returns agent code and account details
```

#### Document Upload
```php
POST /trusted/agent_document
// Uploads agent documents to HQ
// Supports multiple file types and tagging
```

#### Agent Configuration (B2B)
```php
POST /trusted/agents/{agent_code}/config
// Stores carrier selections and preferences
// B2B-specific configuration options
```

### NIPR API Integration

The application integrates with NIPR (National Insurance Producer Registry) for NPN verification and license validation:

#### NIPR Service Features
- **NPN Lookup by SSN**: Verify agent NPN using last 4 digits of SSN and name
- **NPN Lookup by License**: Verify agent NPN using license number and state
- **License Validation**: Confirm active insurance licenses
- **Producer Type Verification**: Validate producer type (Individual, Business Entity, etc.)

#### NIPR API Methods

**SSN-Based Lookup**
```php
// NiprSoapService::NPNLookupWithSSN($ssnLast4, $firstName, $lastName)
$npn = $niprService->NPNLookupWithSSN('1234', 'John', 'Doe');
```

**License-Based Lookup**
```php
// NiprSoapService::NPNLookupWithLicenseNumber($licenseId, $state)
$npn = $niprService->NPNLookupWithLicenseNumber('L123456', 'FL');
```

#### NIPR Integration Workflow
1. **Agent Application**: User provides SSN last 4 digits or license information
2. **API Call**: System calls NIPR SOAP service for verification
3. **NPN Retrieval**: NIPR returns NPN if agent is found
4. **Validation**: System validates NPN and stores in user metadata
5. **Approval Process**: Verified NPN enables application approval

#### NIPR Configuration Requirements
- **Production URL**: `https://npn-ws.api.nipr.com`
- **WSDL Endpoint**: `/npn-ws/npnLookup?wsdl`
- **Authentication**: Username/password credentials
- **SOAP Protocol**: XML-based SOAP 1.1 requests

### Data Flow Architecture

```
[Agent Application] → [Laravel API] → [Queue Job] → [HQ API] → [HQ Database]
                                   ↓
[Document Upload] → [Azure Storage] → [HQ Document API]
                                   ↓
[Email Notifications] → [Mandrill] → [Agent/Admin]
                                   ↓
[NPN Verification] → [NIPR SOAP API] → [NPN Database]
```

## Environment Configuration

### Tenant Switching

The application behavior is controlled by the `APP_TENANT` environment variable:

```bash
# Q2A Configuration
APP_TENANT=Q2A
APP_TENANT_ID=124
MIX_APP_TENANT="${APP_TENANT}"

# Branding (Q2A)
MIX_APP_PRIMARY_COLOR_LIGHT="#46C3B2"
MIX_APP_SECONDARY_COLOR_DARK="#005851"

# Q2B Configuration
APP_TENANT=Q2B
APP_TENANT_ID=125
MIX_APP_TENANT="${APP_TENANT}"

# Branding (B2B)
MIX_APP_PRIMARY_COLOR_LIGHT="#1976D2"
MIX_APP_SECONDARY_COLOR_DARK="#0D47A1"
```

### Database Connections

#### Primary Application Database
```bash
DB_CONNECTION=sqlsrv-internal
DB_HOST=your_primary_host
DB_PORT=1433
DB_DATABASE=sfgonboarding_STAGING
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#### HQ System Databases
```bash
# Q2A HQ Connection
MSSQL_HOST=your_hq_host
MSSQL_DATABASE=HQ_QLT_DEV
MSSQL_USERNAME=np_au_quility_hq
MSSQL_PASSWORD=your_hq_password
MSSQL_PORT=1433

# B2B HQ Connection
MSSQL_B2B_HOST=your_b2b_host
MSSQL_B2B_DATABASE=HQ_B2B_DEV
MSSQL_B2B_USERNAME=np_au_b2b_hq
MSSQL_B2B_PASSWORD=your_b2b_password
MSSQL_B2B_PORT=1433
```

### Auth0 Configuration

#### Q2A Auth0 Setup
```bash
AUTH0_DOMAIN=https://your-auth0-domain.auth0.com
AUTH0_CLIENT_ID=your_q2a_client_id
AUTH0_CLIENT_SECRET=your_q2a_client_secret
AUTH0_AUDIENCE=http://localhost:8080/api
AUTH0_REDIRECT_URI=https://quilityonboarding.test/auth0/callback
```

#### B2B Auth0 Setup
```bash
AUTH0_HQ_DOMAIN=auth-dev.quility.com
AUTH0_HQ_CLIENT_ID=your_b2b_client_id
AUTH0_HQ_CLIENT_SECRET=your_b2b_client_secret
AUTH0_HQ_API_IDENTIFIER=http://localhost:8080/api
```

### External Service Configuration

#### HQ Account Integration
```bash
HQ_ACCOUNT_AUTH_PATH="https://your-auth0-domain.auth0.com/oauth/token"
HQ_ACCOUNT_CREATION_PATH="https://dev-dashboard.quility.com/api/"
HQ_ACCOUNT_AUDIENCE_PATH="http://localhost:8080/api"

QUILITYACCOUNT_CLIENT_ID=your_hq_client_id
QUILITYACCOUNT_CLIENT_SECRET=your_hq_client_secret
```

#### NIPR API Configuration
```bash
NIPR_BASE_URL=https://npn-ws.api.nipr.com
NIPR_WSDL_URL=https://npn-ws.api.nipr.com/npn-ws/npnLookup?wsdl
NIPR_USERNAME=your_nipr_username
NIPR_PASSWORD=your_nipr_password
```

#### HelloSign Configuration
```bash
HELLOSIGN_CLIENT_ID=your_hellosign_client_id
HELLOSIGN_API_KEY=your_hellosign_api_key
MIX_HELLOSIGN_SKIP_DOMAIN_VERIFICATION=false
```

#### Azure Storage Configuration
```bash
AZURE_STORAGE_NAME=agentdocuments
AZURE_STORAGE_KEY=your_azure_storage_key
AZURE_STORAGE_CONTAINER=dev
```

#### Email Configuration (Mandrill)
```bash
MANDRILL_KEY=your_mandrill_key
MAIL_MAILER=mandrill
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Company Name"
```

### WebSocket Configuration
```bash
LARAVEL_WEBSOCKETS_PATH=wss://your-domain.com:6001/socket
MIX_LARAVEL_WEBSOCKETS_PATH="${LARAVEL_WEBSOCKETS_PATH}"
```

## Developer Setup Guide

### Prerequisites

- **PHP**: 8.1 or higher *(currently using 8.1)*
- **Node.js**: 14.x or higher *(currently using 14.x)*
- **Composer**: Latest version
- **Redis**: For caching and queues
- **SQL Server**: Access to development databases
- **Git**: Version control

### Installation Steps

#### 1. Clone Repository
```bash
git clone https://github.com/your-org/quilityonboarding.git
cd quilityonboarding
```

#### 2. Install PHP Dependencies
```bash
composer install
```

#### 3. Install Node Dependencies
```bash
npm install
```

#### 4. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure your .env file with appropriate values
# See Environment Configuration section above
```

#### 5. Database Setup
```bash
# Run migrations
php artisan migrate

# Seed database (optional)
php artisan db:seed
```

#### 6. Storage Setup
```bash
# Create storage link
php artisan storage:link

# Set proper permissions
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

#### 7. Build Frontend Assets
```bash
# Development build
npm run dev

# Watch for changes
npm run watch

# Production build
npm run production
```

#### 8. Queue Setup
```bash
# Start Redis server
redis-server

# Start Laravel Horizon (for queue processing)
php artisan horizon

# Or use basic queue worker
php artisan queue:work
```

#### 9. WebSocket Setup (Optional)
```bash
# Start WebSocket server
php artisan websockets:serve
```

### Local Development Workflow

#### Starting Development Environment
```bash
# Terminal 1: Start Laravel development server
php artisan serve

# Terminal 2: Start asset compilation with hot reload
npm run hot

# Terminal 3: Start queue processing
php artisan horizon

# Terminal 4: Start WebSocket server (if needed)
php artisan websockets:serve
```

#### Switching Between Q2A and Q2B

To switch between tenant versions:

1. **Update Environment File**:
```bash
# For Q2A
APP_TENANT=Q2A
APP_TENANT_ID=124

# For Q2B
APP_TENANT=Q2B
APP_TENANT_ID=125
```

2. **Clear Configuration Cache**:
```bash
php artisan config:clear
php artisan cache:clear
```

3. **Rebuild Frontend Assets**:
```bash
npm run dev
```

#### Database Management

```bash
# Create new migration
php artisan make:migration create_example_table

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Fresh migration with seeding
php artisan migrate:fresh --seed

# Check migration status
php artisan migrate:status
```

## Database Schema Overview

### Core Tables

#### Users Table
Primary table for all system users (agents, candidates, admins):

```sql
users
├── id (UUID, Primary Key)
├── name (VARCHAR)
├── email (VARCHAR, Unique)
├── sub (VARCHAR, Auth0 Subject ID)
├── agent_code (VARCHAR, HQ Agent Code)
├── inviter_user_id (UUID, Foreign Key)
├── inviter_agent_code (VARCHAR)
├── agency_owner_code (VARCHAR)
├── upline_agent_id (VARCHAR)
├── approved_by_ao (BOOLEAN)
├── contract_level (VARCHAR)
├── work_email (VARCHAR)
├── licensed (TIMESTAMP)
├── first_ao_submission (TIMESTAMP)
├── homeoffice_submission (TIMESTAMP)
└── auth0_metadata (JSON)
```

#### User Invites Table
Manages the invitation process for new agents:

```sql
user_invites
├── id (UUID, Primary Key)
├── user_id (UUID, Foreign Key to users)
├── email (VARCHAR)
├── name (VARCHAR)
├── phone (VARCHAR)
├── code (VARCHAR, Invitation Code)
├── contract_level (VARCHAR)
├── upline_agent_id (VARCHAR)
├── type (VARCHAR, b2b-principal, etc.)
├── carrier (JSON, Selected Carriers)
├── agreement_type (VARCHAR, Standard/Custom)
├── invite_sent (TIMESTAMP)
├── invite_accepted (TIMESTAMP)
└── blocked (BOOLEAN)
```

#### User Remote Accounts Table
Tracks HQ system account creation:

```sql
user_remote_accounts
├── id (UUID, Primary Key)
├── user_id (UUID, Foreign Key to users)
├── type (VARCHAR, licensed/unlicensed)
├── status (VARCHAR, created/failed)
├── agent_code (VARCHAR)
├── opt_id (VARCHAR, HQ Login ID)
├── opt_pass (VARCHAR, Encrypted)
└── hq_metadata (JSON)
```

### Form System Tables

#### Form Pages
Defines the multi-step application structure:

```sql
form_pages
├── id (UUID, Primary Key)
├── label (VARCHAR)
├── status (VARCHAR, active/disabled)
├── step_ident (VARCHAR, Unique Step Identifier)
├── subline (TEXT, Description)
├── sort (INTEGER, Display Order)
└── application_slug (VARCHAR, Tenant-specific)
```

#### Form Sections
Groups related questions within a page:

```sql
form_sections
├── id (UUID, Primary Key)
├── form_page_id (UUID, Foreign Key)
├── label (VARCHAR, Section Title)
├── status (VARCHAR)
├── sort (INTEGER)
├── active_start (TIMESTAMP, Conditional Display)
├── active_end (TIMESTAMP, Conditional Display)
└── fill_mode (VARCHAR, manual/google_autocomplete)
```

#### Form Fields
Individual form inputs and questions:

```sql
form_fields
├── id (UUID, Primary Key)
├── form_section_id (UUID, Foreign Key)
├── label (VARCHAR)
├── type (VARCHAR, text/select/upload/etc.)
├── width (VARCHAR, full-width/half-width/etc.)
├── is_required (BOOLEAN)
├── is_secure (BOOLEAN, PII Data)
├── max_length (INTEGER)
├── placeholder (VARCHAR)
├── sort (INTEGER)
└── alias_autofill (VARCHAR, Auto-population)
```

### Integration Tables

#### User Signatures
Tracks HelloSign signature events:

```sql
user_signatures
├── id (UUID, Primary Key)
├── user_email (VARCHAR)
├── signature_id (VARCHAR, HelloSign ID)
├── account_id (VARCHAR)
├── app_id (VARCHAR)
├── event_type (VARCHAR)
├── event_hash (VARCHAR)
└── event_time (VARCHAR)
```

### HQ System Views

The application connects to HQ system views for agent data:

- `vw_QuilityAgent`: Agent information and hierarchy
- `v_Applications`: Application status and details
- `AgentLevel`: Agent contract levels and permissions
- `AgentHierarchy`: Upline/downline relationships

## API Documentation

### Authentication

All API endpoints require authentication via Auth0 JWT tokens:

```javascript
// Request Headers
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### Core API Endpoints

#### User Management

**Get Current User**
```http
GET /api/user
```

**Update User Profile**
```http
PUT /api/user
Content-Type: application/json

{
  "name": "John Doe",
  "work_email": "<EMAIL>"
}
```

#### Application Management

**Get Application Form**
```http
GET /api/form/{page_id}
```

**Submit Application Data**
```http
POST /api/form/{page_id}
Content-Type: application/json

{
  "field_id": "field_value",
  "another_field": "another_value"
}
```

**Get Application Status**
```http
GET /api/application/status
```

#### Document Management

**Upload Document**
```http
POST /api/file/upload
Content-Type: multipart/form-data

file: {binary_file_data}
field_id: {form_field_id}
```

**Get Document**
```http
GET /api/storage/uploads/files/{path}/{filename}
```

#### Agent Management (Admin Only)

**Get All Applications**
```http
GET /api/private/applications
```

**Approve Application**
```http
POST /api/private/applications/{user_id}/approve
```

**Create HQ Account**
```http
POST /api/private/applications/{user_id}/create-hq-account
```

**Upload Files to HQ**
```http
POST /api/private/applications/{user_id}/upload-files-to-hq
```

#### Invitation Management

**Send Invitation**
```http
POST /api/private/invitations
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "Jane Candidate",
  "phone": "************",
  "contract_level": "Agent",
  "type": "standard",
  "carrier": ["Carrier1", "Carrier2"]
}
```

**Get Invitation Status**
```http
GET /api/private/invitations/{invitation_id}
```

#### NPN Verification (NIPR Integration)

**Verify NPN**
```http
POST /api/application/verify_npn
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "ssn_last_4": "1234",
  "licenseId": "L123456",
  "state": "FL",
  "staff_override": false
}
```

**Response Format**
```json
{
  "npn": "********",
  "exists": true,
  "message": "NPN verified successfully",
  "license_info": {
    "state": "FL",
    "license_number": "L123456",
    "status": "Active"
  }
}
```

**Error Response**
```json
{
  "npn": null,
  "exists": false,
  "message": "No NPN found for provided information",
  "error": "NIPR lookup failed"
}
```

### WebSocket Events

The application uses Laravel WebSockets for real-time updates:

#### Connection
```javascript
// Frontend connection
const echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY,
    wsHost: window.location.hostname,
    wsPort: 6001,
    forceTLS: false,
    disableStats: true,
});
```

#### Available Channels
- `application.{user_id}`: Application status updates
- `notifications.{user_id}`: General notifications
- `admin.applications`: Admin application updates

### Error Handling

API responses follow consistent error format:

```json
{
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  },
  "status": 422
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Server Error

## Application Content Management

The application uses a sophisticated content management system for handling onboarding questions and forms through the **ApplicationContentPatch** system.

### ApplicationContentPatch System

The ApplicationContentPatch system allows developers to add, modify, or update application questions and form structure through versioned command patches.

#### Directory Structure
```
app/Console/Commands/ApplicationContentPatch/
├── _Run.php                           # Master patch runner
├── AddB2bAgentApplicationQuestions.php
├── AddNewLegalQuestions.php
├── AddPrincipalApplicationQuestions.php
├── AddLifeLoaQuestion.php
├── AddCarrierContractQuestions.php
├── UpdateUsCitizenToSelect.php
└── ... (other patch files)
```

#### How It Works

1. **Patch Commands**: Each patch is a Laravel Artisan command that modifies the form structure
2. **Version Control**: Patches are numbered and tracked to prevent duplicate execution
3. **Idempotent**: Patches check if they've already been run to avoid conflicts
4. **Master Runner**: The `_Run.php` command executes all registered patches in sequence

#### Creating New Application Questions

##### Step 1: Create a New Patch Command

```bash
php artisan make:command ApplicationContentPatch/AddYourNewQuestions
```

##### Step 2: Implement the Patch

```php
<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class AddYourNewQuestions extends Command
{
    protected $signature = 'app-content-patch:XX:add-your-new-questions';
    protected $description = 'Application Content Patch - Add new questions';

    public function handle()
    {
        $this->line("[app-content-patch:XX:add-your-new-questions] Start");

        // Check if patch already ran
        $exists = FormSection::where('label', 'Your New Question Text')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        // Get the target page
        $page = FormPage::where('step_ident', 'target-page-identifier')->first();
        if(!$page) {
            $this->error("Can't find target page");
            return;
        }

        $sort = 10; // Adjust sort order as needed

        // Create section with question
        $section = FormBuilder::createSection(
            'Your New Question Text?',
            $page->id,
            $sort
        );

        // Create form field
        $field = FormBuilder::createField(
            '', // Label (empty if using section label)
            FormField::TYPE_SELECT, // Field type
            FormField::WIDTH_HALF, // Field width
            $section->id,
            $sort++,
            'Please Select', // Placeholder
            1, // Required
            0, // Not secure
            1, // Max length
            FormField::SELECT_BOOL // Options (Yes/No)
        );

        // Create conditional subsection (if needed)
        $subSection = FormBuilder::createSection(
            'Additional information based on answer',
            $page->id,
            $sort++,
            $field->id, // Parent field
            FormField::SELECT_BOOL_YES // Show when "Yes" selected
        );

        $this->line("[app-content-patch:XX:add-your-new-questions] Finish");
        return 0;
    }
}
```

##### Step 3: Register the Patch

Add your new patch to the `$registeredPatches` array in `_Run.php`:

```php
protected $registeredPatches = [
    // ... existing patches
    'app-content-patch:XX:add-your-new-questions',
];
```

##### Step 4: Run the Patch

```bash
# Run single patch
php artisan app-content-patch:XX:add-your-new-questions

# Run all patches
php artisan app-content-patch:run
```

#### Available Form Field Types

```php
// Text inputs
FormField::TYPE_TEXT
FormField::TYPE_EMAIL
FormField::TYPE_PHONE
FormField::TYPE_SSN
FormField::TYPE_PASSWORD

// Selection fields
FormField::TYPE_SELECT
FormField::TYPE_CHECKBOX

// Date fields
FormField::TYPE_DATE

// File uploads
FormField::TYPE_UPLOAD

// Rich content
FormField::TYPE_HTML
FormField::TYPE_TEXTAREA
FormField::TYPE_SIGNATURE
```

#### Field Width Options

```php
FormField::WIDTH_FULL      // 100% width
FormField::WIDTH_HALF      // 50% width
FormField::WIDTH_THIRD     // 33% width
FormField::WIDTH_FOURTH    // 25% width
```

#### Common Select Options

```php
// Boolean Yes/No
FormField::SELECT_BOOL = [
    'yes' => 'Yes',
    'no' => 'No'
];

// States
FormField::SELECT_STATES = [
    'AL' => 'ALABAMA',
    'AK' => 'ALASKA',
    // ... all states
];

// Custom options
$customOptions = [
    'option1' => 'Display Text 1',
    'option2' => 'Display Text 2',
];
```

#### Conditional Logic

You can create sections that show/hide based on other field values:

```php
// Create conditional section
$conditionalSection = FormBuilder::createSection(
    'This shows when parent field equals specific value',
    $page->id,
    $sort++,
    $parentField->id,        // Parent field ID
    'expected_value',        // Value that triggers display
    'show',                  // Action (show/hide)
    'equals'                 // Condition type
);
```

#### Best Practices for Content Patches

1. **Always Check for Existing Content**: Prevent duplicate patches
2. **Use Descriptive Names**: Make patch purpose clear
3. **Increment Version Numbers**: Follow sequential numbering
4. **Test Thoroughly**: Verify patch works in both Q2A and Q2B tenants
5. **Document Changes**: Add comments explaining complex logic
6. **Handle Errors Gracefully**: Check for required dependencies

#### Example: Adding B2B-Specific Questions

```php
public function handle()
{
    // Only run for B2B tenant
    if (config('app.tenant') !== 'Q2B') {
        $this->warn("This patch is only for B2B tenant.");
        return;
    }

    // Check if B2B page exists
    $page = FormPage::where('application_slug', 'b2b-agent')->first();
    if (!$page) {
        $this->error("B2B application page not found");
        return;
    }

    // Add B2B-specific questions
    $section = FormBuilder::createSection(
        'B2B Partner Information',
        $page->id,
        $sort++
    );

    // Partner type selection
    $partnerField = FormBuilder::createField(
        'Partner Type',
        FormField::TYPE_SELECT,
        FormField::WIDTH_HALF,
        $section->id,
        $sort++,
        'Select Partner Type',
        1, 0, 1,
        [
            'agency' => 'Insurance Agency',
            'broker' => 'Insurance Broker',
            'firm' => 'Financial Services Firm'
        ]
    );
}
```

### Form Builder Utility Methods

The `FormBuilder` class provides convenient methods for creating form elements:

```php
// Create a new form page
FormBuilder::createPage($label, $stepIdentifier);

// Create a form section
FormBuilder::createSection($label, $pageId, $sort, $parentFieldId, $parentValue);

// Create a form field
FormBuilder::createField($label, $type, $width, $sectionId, $sort, $placeholder, $required, $secure, $maxLength, $options);

// Add form lookup for data population
FormBuilder::addFormLookup($fieldId, $slug);
```

## Development Workflow

### Code Organization

#### Backend Structure
```
app/
├── Console/Commands/           # Artisan commands
├── Http/Controllers/          # API controllers
├── Models/                    # Eloquent models
├── Jobs/                      # Queue jobs
├── Services/                  # Business logic services
├── Providers/                 # Service providers
└── CustomProviders/           # Custom service implementations

config/                        # Configuration files
database/
├── migrations/                # Database migrations
└── seeders/                   # Database seeders

routes/
├── api.php                    # API routes
├── web.php                    # Web routes
└── auth.php                   # Authentication routes
```

#### Frontend Structure
```
resources/js/src/
├── components/                # Vue components
├── views/                     # Page components
├── store/                     # Vuex store modules
│   ├── modules/               # Store modules
│   └── API/                   # API service classes
├── router/                    # Vue Router configuration
├── plugins/                   # Vue plugins
└── utils/                     # Utility functions
```

### NIPR Development and Testing

#### NIPR Testing Commands

The application includes Artisan commands for testing NIPR integration:

```bash
# Test NPN lookup with SSN
php artisan nipr:npn-lookup "John" "Doe" "1234"

# Test NPN lookup with license number (modify command as needed)
php artisan nipr:npn-lookup --license="L123456" --state="FL"
```

#### NIPR Environment Setup

**Development Environment**
```bash
# Use NIPR beta environment for development
NIPR_BASE_URL=https://npn-ws.api.beta.nipr.com
NIPR_WSDL_URL=https://npn-ws.api.beta.nipr.com/npn-ws/npnLookup?wsdl
NIPR_USERNAME=beta_username
NIPR_PASSWORD=beta_password
```

**Production Environment**
```bash
# Use NIPR production environment
NIPR_BASE_URL=https://npn-ws.api.nipr.com
NIPR_WSDL_URL=https://npn-ws.api.nipr.com/npn-ws/npnLookup?wsdl
NIPR_USERNAME=production_username
NIPR_PASSWORD=production_password
```

#### NIPR Error Handling

Common NIPR integration issues and solutions:

1. **SOAP Connection Errors**
   - Verify WSDL URL accessibility
   - Check network connectivity and firewall rules
   - Validate SSL certificates

2. **Authentication Failures**
   - Confirm username/password credentials
   - Check account status with NIPR
   - Verify API access permissions

3. **No NPN Found**
   - Validate input data format
   - Check SSN last 4 digits accuracy
   - Verify license number and state combination

4. **Rate Limiting**
   - Implement request throttling
   - Cache successful lookups
   - Handle rate limit responses gracefully

#### NIPR Service Monitoring

```php
// Health check for NIPR service
public function checkNiprHealth()
{
    try {
        $niprService = resolve(NiprSoapService::class);
        $functions = $niprService->getFunctionList();
        return response()->json(['status' => 'healthy', 'functions' => count($functions)]);
    } catch (\Exception $e) {
        return response()->json(['status' => 'unhealthy', 'error' => $e->getMessage()], 500);
    }
}
```

### Development Best Practices

#### Backend Development

1. **Follow Laravel Conventions**
   - Use Eloquent relationships properly
   - Implement proper validation
   - Use Form Requests for complex validation
   - Follow PSR-4 autoloading standards

2. **Database Best Practices**
   - Always use migrations for schema changes
   - Use proper foreign key constraints
   - Index frequently queried columns
   - Use UUIDs for primary keys

3. **API Development**
   - Use consistent response formats
   - Implement proper error handling
   - Use Laravel Resources for data transformation
   - Document all endpoints

4. **Queue Jobs**
   - Make jobs idempotent
   - Handle failures gracefully
   - Use appropriate queue priorities
   - Monitor job performance

#### Frontend Development

1. **Vue.js Best Practices**
   - Use single-file components
   - Implement proper prop validation
   - Use computed properties for derived data
   - Follow Vue style guide

2. **State Management**
   - Keep Vuex store modules focused
   - Use actions for async operations
   - Implement proper error handling
   - Use getters for computed state

3. **Component Organization**
   - Create reusable components
   - Use proper component naming
   - Implement proper event handling
   - Use slots for flexible layouts

### Testing Strategy

#### Backend Testing

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage
```

#### Frontend Testing

```bash
# Run JavaScript tests
npm run test-js

# Run Vue component tests
npm run test-vue

# Run all tests
npm test
```

#### Test Structure

```
tests/
├── Feature/                   # Integration tests
│   ├── AuthenticationTest.php
│   ├── ApplicationTest.php
│   └── HQIntegrationTest.php
├── Unit/                      # Unit tests
│   ├── Models/
│   ├── Services/
│   └── Jobs/
└── Browser/                   # Browser tests (Laravel Dusk)
```

### Code Quality Tools

#### PHP Code Quality
```bash
# PHP CS Fixer
./vendor/bin/php-cs-fixer fix

# PHPStan static analysis
./vendor/bin/phpstan analyse

# Laravel IDE Helper
php artisan ide-helper:generate
```

#### JavaScript Code Quality
```bash
# ESLint
npm run lint

# Prettier formatting
npm run lint:prettier
```

### Git Workflow and Deployment Process

#### Feature Branch Workflow

The project follows a structured branching and deployment process:

1. **Feature Development**
   ```bash
   # Create feature branch from main
   git checkout main
   git pull origin main
   git checkout -b feature/your-feature-name

   # Develop your feature
   # ... make changes and commits

   # Push feature branch
   git push origin feature/your-feature-name
   ```

2. **Integration Process**
   ```bash
   # Merge feature to develop branch
   git checkout develop
   git pull origin develop
   git merge feature/your-feature-name
   git push origin develop

   # Merge develop to staging
   git checkout staging
   git pull origin staging
   git merge develop
   git push origin staging

   # Finally merge staging to main
   git checkout main
   git pull origin main
   git merge staging
   git push origin main
   ```

#### Deployment Pipeline

**Automatic Deployments (Laravel Forge)**
- **Develop Branch**: Auto-deploys to development environment
- **Staging Branch**: Auto-deploys to staging environment

**Manual Deployment (Laravel Forge)**
- **Main Branch**: Requires manual deployment trigger in Forge UI
  1. Log into Laravel Forge
  2. Navigate to the project
  3. Click "Deploy Now" button
  4. Monitor deployment logs

#### Post-Deployment Steps

**WebSocket Service Setup**
After deploying to main/production environments, you may need to restart the WebSocket service for live application review status syncing:

1. **Access Forge Commands**
   - Log into Laravel Forge
   - Navigate to your project
   - Go to "Commands" section in the UI

2. **Run WebSocket Command**
   ```bash
   php artisan websockets:serve &
   ```

3. **Command History**
   - You can find this command in the Forge UI command history
   - Look for previous executions to copy the exact command
   - The `&` runs the process in the background

**Alternative WebSocket Management**
```bash
# Check if WebSocket service is running
ps aux | grep websockets

# Kill existing WebSocket processes (if needed)
pkill -f "websockets:serve"

# Start WebSocket service in background
nohup php artisan websockets:serve > /dev/null 2>&1 &
```

#### Branch Protection and Review Process

**Recommended Branch Protection Rules:**
- **Main Branch**: Require pull request reviews before merging
- **Staging Branch**: Require status checks to pass
- **Develop Branch**: Allow direct pushes for rapid development

**Code Review Checklist:**
- [ ] Feature works in both Q2A and Q2B tenants
- [ ] Database migrations are reversible
- [ ] Tests pass for new functionality
- [ ] NIPR integration tested (if applicable)
- [ ] HQ integration verified (if applicable)
- [ ] Frontend builds without errors
- [ ] No sensitive data exposed in logs

## Testing

### Test Environment Setup

#### Database Testing
```bash
# Create test database
php artisan migrate --env=testing

# Run tests with fresh database
php artisan test --recreate-databases
```

#### Mock External Services
```php
// Mock HQ API calls
Http::fake([
    'https://dev-dashboard.quility.com/api/*' => Http::response([
        'data' => ['agent' => ['AgentCode' => 'TEST123']]
    ], 200)
]);

// Mock Auth0
Auth0::shouldReceive('getUser')->andReturn([
    'sub' => 'auth0|test123',
    'email' => '<EMAIL>'
]);

// Mock NIPR SOAP Service
$this->mock(NiprSoapService::class, function ($mock) {
    $mock->shouldReceive('NPNLookupWithSSN')
         ->with('1234', 'John', 'Doe')
         ->andReturn('********');

    $mock->shouldReceive('NPNLookupWithLicenseNumber')
         ->with('L123456', 'FL')
         ->andReturn('********');
});
```

### Key Test Cases

#### Authentication Tests
- Auth0 token validation
- User creation and updates
- Permission-based access control
- Tenant-specific authentication

#### Application Flow Tests
- Multi-step form submission
- Document upload and validation
- Application approval workflow
- HQ account creation

#### Integration Tests
- HQ API communication
- NIPR NPN verification and license validation
- HelloSign contract execution
- Email notification delivery

## Deployment

### Production Environment

#### Server Requirements
- **PHP**: 8.1+ with required extensions
- **Web Server**: Nginx or Apache
- **Database**: SQL Server 2019+
- **Redis**: For caching and queues
- **Node.js**: For asset compilation
- **SSL Certificate**: Required for Auth0 and HelloSign

#### Environment Configuration

```bash
# Production settings
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-production-domain.com

# Database connections
DB_CONNECTION=sqlsrv
# ... production database credentials

# Cache and sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# File storage
FILESYSTEM_DISK=azure
```

#### Deployment Steps

1. **Code Deployment**
```bash
# Pull latest code
git pull origin main

# Install dependencies
composer install --no-dev --optimize-autoloader

# Clear and cache config
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run migrations
php artisan migrate --force
```

2. **Asset Compilation**
```bash
npm ci
npm run production
```

3. **Queue Management**
```bash
# Restart Horizon
php artisan horizon:terminate

# Or restart queue workers
sudo supervisorctl restart laravel-worker:*
```

4. **Cache Management**
```bash
php artisan cache:clear
php artisan config:cache
```

### Monitoring and Maintenance

#### Application Monitoring
- Laravel Horizon for queue monitoring
- Application logs via Laravel logging
- Performance monitoring with APM tools
- Database query monitoring

#### Health Checks
- Database connectivity
- HQ API availability
- Redis connectivity
- File storage accessibility

#### Backup Strategy
- Database backups (automated)
- File storage backups
- Configuration backups
- Code repository backups

### Security Considerations

#### Data Protection
- PII data encryption at rest
- Secure file upload validation
- SQL injection prevention
- XSS protection

#### Access Control
- Role-based permissions
- API rate limiting
- Auth0 security policies
- Network security (VPN, firewalls)

#### Compliance
- Data retention policies
- Audit logging
- GDPR compliance (if applicable)
- Industry-specific regulations

---

## Support and Documentation

For additional support or questions:

1. **Technical Issues**: Contact the development team
2. **Business Logic**: Consult with product owners
3. **HQ Integration**: Coordinate with HQ system administrators
4. **Auth0 Issues**: Check Auth0 dashboard and logs

### Additional Resources

- [Laravel Documentation](https://laravel.com/docs)
- [Vue.js Documentation](https://vuejs.org/guide/)
- [Vuetify Documentation](https://vuetifyjs.com/)
- [Auth0 Documentation](https://auth0.com/docs)
- [HelloSign API Documentation](https://developers.hellosign.com/)

---

*Last Updated: 2024*


