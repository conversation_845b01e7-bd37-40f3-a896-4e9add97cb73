<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\UserEntry;
use App\Models\FormPage;
use App\Models\UserInvite;
use App\Traits\UsesEncryption;

use App\Events\Review\AO\Submitted as AOSubmitted;
use App\Events\Review\AO\Rejected as AORejected;
use App\Events\Review\AO\Approved as AOApproved;
use App\Events\Review\HO\Submitted as HOSubmitted;
use App\Events\Review\HO\Rejected as HORejected;
use App\Events\Review\HO\Approved as HOApproved;
use App\Events\Status\Unenrolled;
use App\Events\Status\Enrolled;

use App\Events\Review\RevisionRequested;

use App\Events\Onboarding\UnlicensedAccountCreated;
use App\Events\Onboarding\AssignedToDept;
use App\Models\UserTask;
use Exception;
use Log;
use UserTasks;
use Agents;
use URL;
use Storage;
use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\UserStatusHistory;
use App\Models\UserUpload;
use DB;
use Illuminate\Support\Facades\App;
use FormBuilder;
use HQAccounts;
use App\Mail\Review\AO\SendRevisionRequestedEmail;
use App\Mail\Review\AO\SendReminderEmail;
use App\Models\Agent;
use App\Models\AgentB2B;
use App\Models\UserRemoteAccount;
use App\Models\UserStatus;
use App\Models\UserApplications;
use App\Models\AgentConfig;
use Illuminate\Support\Facades\Mail;

use Illuminate\Support\Facades\Hash;

use Carbon\Carbon;
use App\Services\AgentSyncApiService;
use League\Flysystem\AzureBlobStorage\AzureBlobStorageAdapter;
use Illuminate\Support\Str;


class UserApplicationController extends Controller
{
    use UsesEncryption;

    private $logger;
    private $tenant;

    public function __construct()
    {
        $this->logger = app('logger.task-generator');
        $this->tenant = config('app.tenant');
    }

    public function status()
    {
        $data = [
            'status' => Auth::user()->status
        ];

        return response()->json($data);
    }

    public function mostRecentStep()
    {
        $user = Auth::user();
        $user_entry = UserEntry::where('user_id', $user->id)->with('formField.formSection.formPage')->orderBy('updated_at', 'desc')->first();

        if($user_entry) {
            $current_page = $user_entry->formField->formSection->formPage;
        } else {
            $current_page = null;;
        }

        $data = [
            'current_page' => $current_page
        ];

        return response()->json($data);
    }

    public function currentStep()
    {
        $status = Auth::user()->status;
        if(!$status)
            abort(404);

        $page = FormPage::where('id', $status->form_page_id)->first();
        $data = [
            'current_page' => $page,
        ];

        return response()->json($data);
    }

    public function furthestStep()
    {
        $user = Auth::user();
        $pages = FormPage::with('formFields')->where('sort', '!=', null)->orderBy('sort', 'asc')->get();

        $user_entries = UserEntry::where('user_id', $user->id)->get();
        $user_entries_fields = $user_entries->pluck('form_field_id');
        $current_page = null;
        foreach($pages as $page) {
            $page_fields = $page->formFields->pluck('id');
            if($user_entries_fields->intersect($page_fields)->count()) {
                $current_page = $page;
            }
        }

        if($current_page) {
            $current_page->setRelation('form_fields', null);
        }

        $data = [
            'current_page' => $current_page
        ];

        return response()->json($data);
    }

    /*
     * application list for tracking
     */
    public function getList(Request $request)
    {
        $user = $request->user();
        // $flagCompleted = $request->completed ?? "";

        $page = $request->page ?? 1;
        $itemsPerPage = $request->itemsPerPage ?? 10;
        $start = ($page - 1) * $itemsPerPage;

        $apps = [];
        $query = User::selectRaw('users.id as user_id, users.name as user_name, user_statuses.name as status_name, user_statuses.slug as status_slug, user_statuses.form_page_id, agency_owner_name, upline_agent_name, last_revision, passed_exam, licensed, homeoffice_submission, first_homeoffice_submission, users.email, approved_by_ao, last_approved, first_ao_submission, users.upline_agent_id, recruiter_name, users.created_at, users.updated_at, is_returning, DATEDIFF(day, users.updated_at, GETDATE()) AS application_age, reminders, users.last_reminder_date, DATEDIFF(day, users.last_reminder_date, GETDATE()) AS last_reminder_sent, users.phone, users.last_name, users.dob, users.ssn, invite.advanced_markets as advanced_markets, users.transferring, users.returning, users.user_invite_id as user_invite_id, users.flagged, users.pathway')
            ->join('user_statuses', 'user_statuses.id', '=', 'users.user_status_id')
            // ->leftJoin('meta', function($join)
            //     {
            //         $join->on('meta.metable_id', '=', 'users.id');
            //         $join->where('meta.key','=', 'blacklist');
            //     })
            // ->leftJoin('user_entries as phone', function($join) 
            //     {
            //         $join->on('phone.user_id', '=', 'users.id');
            //         $join->where('phone.form_field_id', '=', '6D12E323-289C-452D-AF25-04D5E59DCD4E');    //form_field_id for mobile phone number
            //     })
            // ->leftJoin('user_entries as last_name', function($join) 
            //     {
            //         $join->on('last_name.user_id', '=', 'users.id');
            //         $join->where('last_name.form_field_id', '=', 'FF428343-C262-4601-994C-1D8F5693CE90');    //form_field_id for last name
            //     })
            // ->leftJoin('user_entries as dob', function($join) 
            //     {
            //         $join->on('dob.user_id', '=', 'users.id');
            //         $join->where('dob.form_field_id', '=', '3A09A658-2A8C-42C6-9161-73D40C4F8DAD');    //form_field_id for birthdate
            //     })
            // ->leftJoin('user_entries as ssn', function($join) 
            //     {
            //         $join->on('ssn.user_id', '=', 'users.id');
            //         $join->where('ssn.form_field_id', '=', 'D0142EEA-B3B9-44E0-A582-80662FC31CB7');    //form_field_id for ssn
            //     })
            // ->leftJoin('user_entries as transferring', function($join) 
            //     {
            //         $join->on('transferring.user_id', '=', 'users.id');
            //         $join->where('transferring.form_field_id', '=', '4D883B0C-DFDF-4BAA-9E08-6FB353986C9F');        //form_field_id for transferring from imo
            //         $join->where('transferring.value', '=', 'YES');
            //     })
            // ->leftJoin('user_entries as returning', function($join) 
            //     {
            //         $join->on('returning.user_id', '=', 'users.id');
            //         $join->where('returning.form_field_id', '=', '4D883B0C-DFDF-4BAA-9E08-6FB353986C9F');        //form_field_id for transferring from imo
            //         $join->where('returning.value', '=', 'YES');
            //     })
            // ->leftJoin('user_invites as invite', function($join)
            //     {
            //         $join->on('invite.id', '=', 'users.user_invite_id');
            //     });
            ->leftJoin('user_invites as invite', 'invite.id', '=', 'users.user_invite_id');
        

        if ($request->show_stale == "false" || !$user->hasRole(User::ROLE_TYPE_STAFF) && !$user->hasRole(User::ROLE_TYPE_SUPER_ADMIN))
            $query->where('users.updated_at', '>', Carbon::now()->subDays(60))->where('users.email', 'not like', '%-stale');
            
        if($this->tenant == 'Q2B') {
            $query = $query->whereIn('users.type', ['b2b-principal', 'b2b-agent']);
        } else {
            // default is Q2A, exclude b2b agents
            $query = $query->whereNotIn('users.type', ['b2b-principal', 'b2b-agent']);
        }

        $search_text = str_replace(" ", "%", $request->text);
        if(!empty($request->text)) {
            // $query = $query->where('search_field', 'like', "%{$request->text}%");
            $query = $query->where(function($query) use ($search_text) {
                $query
                    ->where('users.name', 'like', "%{$search_text}%")
                    ->orWhere('users.email', 'like', "%{$search_text}%")
                    ->orWhere('work_email', 'like', "%{$search_text}%")
                    ->orWhere('agent_code', 'like', "%{$search_text}%")
                    ->orWhere('agency_owner_code', 'like', "%{$search_text}%")
                    ->orWhere('agency_owner_name', 'like', "%{$search_text}%")
                    ->orWhere('inviter_agent_code', 'like', "%{$search_text}%")
                    ->orWhere('recruiter_name', 'like', "%{$search_text}%")
                    ->orWhere('upline_agent_code', 'like', "%{$search_text}%")
                    ->orWhere('upline_agent_name', 'like', "%{$search_text}%");
            });
        }

        if(!empty($request->pathway_types)) {
            $pathwayTypes = explode(',', $request->pathway_types);
            $query = $query->where(function($query) use ($pathwayTypes) {
                foreach ($pathwayTypes as $pathwayType) {
                    $query->orWhere('users.pathway', 'LIKE', '%' . $pathwayType . '%');
                }
            });
        }

        $status = $request->status;
        if($status == 'in-progress')
            $query = $query->whereNotNull('user_statuses.form_page_id');
        elseif($status == 'ao-approved')
            $query = $query->where('approved_by_ao', 1);
        elseif($status == User::ONBOARD_STATUS_EXISTS)
            $query = $query->where('user_statuses.slug', 'LIKE', $status);
        elseif($status == User::ONBOARD_STATUS_SUBMITTED_HO)
            $query = $query->whereIn('user_statuses.slug', [User::ONBOARD_STATUS_SUBMITTED_HO, User::ONBOARD_STATUS_EXISTS_B2BHQ, User::ONBOARD_STATUS_EXISTS_HQ]);
        
        if($status == 'stale' && $request->completed == 'pending') {
            $query->where('users.updated_at', '<', Carbon::now()->subDays(60))
                ->where('user_statuses.slug', User::ONBOARD_STATUS_SUBMITTED_HO);
        }
        //     $query = $query->where(function($query) {
        //         $query->where('user_statuses.slug', User::ONBOARD_STATUS_HO_APPROVED)
        //               ->orWhere('user_statuses.slug', User::ONBOARD_STATUS_EXISTS_B2BHQ);
        //     });
        elseif($status)
            $query = $query->where('user_statuses.slug', $status);


        $use_baseshop = $request->input('use_baseshop') == 'true';
        $possibleUplineAgentIDs = [];
        if ($user->hasRole(User::ROLE_TYPE_STAFF) || $user->hasRole(User::ROLE_TYPE_SUPER_ADMIN)) {
            if($status != User::ONBOARD_STATUS_SUBMITTED_HO && strpos($status, 'flag-exists') === false && (!$status || $status != 'in-progress') && $request->show_all != "true" ) {
                $query = $query->where('approved_by_ao', 1);
            }
        } else {
            //filter results by the current agent and total downline
            if(isset($use_baseshop) && $use_baseshop === true)
            {
                // just use agents in the baseshop
                $agent_ids = $user->getBaseshopAgents()->pluck('AgentID')->all();

                $agent = Agent::where('AgentCode', $user->agent_code)->first();
                array_unshift($agent_ids, $agent->AgentID);

                // make sure own agent code is included
                if(!in_array($user->agent_id, $agent_ids)){
                    $agent_ids[] = $user->agent_id;
                }

                if(count($agent_ids) > 0)
                {
                    $query = $query->whereIn('users.upline_agent_id', $agent_ids);
                } else {
                    return response()->json([
                        'items' => [], 'total' => 0
                    ]);
                }
                
            } else {
                $possibleUplineAgents = collect($user->possibleDownLine());
                if (!$possibleUplineAgents) {
                    return response()->json([
                        'items' => [], 'total' => 0
                    ]);
                }
                $possibleUplineAgentIDs = $possibleUplineAgents->pluck('agent_id');
                // This times out for large downlines. It's faster to just load all current apps (Excludes stale) then filter later
                // if(sizeof($possibleUplineAgentIDs)> 0){
                //     $query = $query->whereIn('upline_agent_id', $possibleUplineAgentIDs);
                // }
            }
        }

        // $total_count = $query->count();

        $order_by = array(
            'registered' => 'users.created_at',
            'agent' => 'users.name',
            'applied_on' => 'first_ao_submission',
            'homeoffice_submission_on' => 'homeoffice_submission',
            'status' => 'status_name',
            'direct_upline' => 'upline_agent_name',
            'agency_owner' => 'agency_owner_name',
            'created_at' => 'users.created_at',
            'recruiter' => 'recruiter_name',
            'updated_at' => 'users.updated_at',
            'completed_on' => 'last_approved',
        );
        
        if($request->sortBy && isset($order_by[$request->sortBy]))
            $query = $query->orderBy($order_by[$request->sortBy], $request->sortDesc == 'true' ? 'desc' : 'asc');

        // handle date ranges
        if($request->date_field != "undefined" && !empty($request->date_field) && !empty($request->date_field) && (!empty($request->start_date) || !empty($request->end_date))) {
            if(!empty($request->start_date) && !empty($request->end_date))
                $query->whereBetween($order_by[$request->date_field], [$request->start_date, $request->end_date]);
            elseif(!empty($request->start_date))
                $query->where($order_by[$request->date_field], '>=', $request->start_date);
            elseif(!empty($request->end_date))
                $query->where($order_by[$request->date_field], '<=', $request->end_date);
        }
        // if(!$request->include_ssn)
        //     $query->limit(5000);

        $total_count = $query->count();

        $results = $query->skip($start)->take($itemsPerPage)->get();
        $results->map(function ($app) use (&$apps, $user, $possibleUplineAgentIDs) {

                $status_slug = $app->status_slug;
                $status_form_page_id = $app->form_page_id;

                // $status = $app->status && $app->status->form_page_id ? 'Application: '.$app->status_name : $app->status_name;
                $status = $app->status_name;;
                if($status_slug == 'submitted-ho')
                    $status = 'Pending Approval by Admin';
                elseif($status_slug == 'submitted-ao')
                    $status = 'Pending Approval by Agency Owner';
                elseif($status_form_page_id)
                    $status = 'Application: '.$status;
                
                $dob = "";
                if(!empty($app->dob)) {
                    $dob_array = explode("-", $app->dob);
                    if(is_array($dob_array) && count($dob_array) == 3)
                        $dob = $dob_array[1]."/".$dob_array[2]."/".$dob_array[0];
                }

                $ssn = "";
                try {
                    // $ssn = !empty($app->ssn) ? ($user->can('view encrypted') ? self::decrypt($app->ssn) : "******") : "";
                    $ssn = !empty($app->ssn) ? self::decrypt($app->ssn) : "";
                } catch(Exception $err) {
                    $ssn = "";
                }

                $invitation = UserInvite::where('id', $app->user_invite_id)->with('group')->first();
                $app->invitation = $invitation;

                if(empty($possibleUplineAgentIDs) || (sizeof($possibleUplineAgentIDs)> 0 && $possibleUplineAgentIDs->contains($app->upline_agent_id))){
                    $apps[] = [
                        'id' => $app->user_id,
                        'agent' => $app->user_name,
                        'last_name' => $app->last_name,
                        'dob' => $dob,
                        'ssn' => $ssn,
                        'status' => $status,
                        'status_slug' => $status_slug,
                        'status_form_page_id' => $status_form_page_id,
                        'agency_owner' => $app->agency_owner_name,
                        'direct_upline' => $app->upline_agent_name,
                        'applied_on' => $app->first_ao_submission,
                        'completed_on' => $app->last_approved,
                        'completed' => in_array($status_slug, [User::ONBOARD_STATUS_AO_APPROVED, User::ONBOARD_STATUS_HO_APPROVED]),
                        'approved_by_ao' => $app->approved_by_ao,
                        'email' => $app->email,
                        'phone' => $app->phone,
                        'is_new_submit' => $app->homeoffice_submission == $app->first_homeoffice_submission,
                        'is_fully_licensed' => $app->licensed ? true : false,
                        'has_passed_exam' => $app->passed_exam ? true : false,
                        'homeoffice_submission_on' => $app->homeoffice_submission,
                        'previously_submitted_ho' => $app->homeoffice_submission != $app->first_homeoffice_submission,
                        'last_revision_requested_on' => $app->last_revision,
                        'all_data' => $app,
                        'recruiter' => $app->recruiter_name,
                        'created_at' => $app->created_at,
                        'updated_at' => $app->updated_at,
                        // 'blacklist' => $app->blacklist,
                        'is_returning' => $app->is_returning,
                        'advanced_markets' => $app->advanced_markets,
                        'transferring' => $app->transferring,
                        'application_age' => $app->application_age,
                        'reminders' => $app->reminders ?? 0,
                        'last_reminder_date' => $app->last_reminder_date ?? '',
                        'last_reminder_sent' => $app->last_reminder_sent ?? '',
                        'license_status' => config('app.tenant') != 'Q2B' ? $this->getLicenseStatus($app) : '',
                        'invitation' => $invitation,
                        'flagged' => $app->flagged,
                        'pathway' => $app->pathway,
                        // 'previous_experience' => FormBuilder::fetchUserEntry($user, FormLookup::Q2A_PREVIOUS_EXPERIENCE),
                        // 'advanced_market_enrollment' => $app->getMeta('advanced_market_enrollment', null)
                    ];
                }
        });

        
        if($request->sortBy == 'license_status') {
            usort($apps, function($a, $b) use ($request) {
                if($request->sortDesc == "true")
                    return strcmp($b['license_status'], $a['license_status']);
                return strcmp($a['license_status'], $b['license_status']);
            });
        }

        return response()->json([
            'items' => $apps,
            'total' => $total_count,
            // 'apps' => $user_apps
        ]);
    }

    private function getLicenseStatus($item)
    {
        $status = '';
        if($item['invitation'] && $item['invitation']['group'])
            $status .= 'G, ';
        if($item['is_returning'])
            $status .= 'R, ';
        if($item['transferring'] == 'YES')
            $status .= 'T, ';
        if($item['advanced_markets'])
            $status .= 'AM, ';
        if($item['licensed'])
            return $status."L";
        if($item['passed_exam'])
            return $status."UP";
        return $status."U";
    }

    /*
     * get overview of application: required in review page
     *
     * @param $id userID of Recruit
     * @return Illumniate\Facades\Response
     */
    public function show($id)
    {
        // TODO: Pull by application ID. For now, we'll pull by User ID.
        // TODO: Enable/disable endpoint based on Auth user permissions/roles.
        $reviewer = Auth::user();

        if (!$user = User::find($id)) {
            abort(404);
        }

        if(!$user->status) {
            abort(404);
        }

        $user_entries = UserEntry::where('user_id', $user->id)->with('formField.formSection')->orderBy('created_at')->get();
        if (!$user_entries) {
            Log::error("REVIEW ERROR: Could not get application entries for user {$user->id}.");
            abort(404);
        }

        $user_entries_mapped = [];
        collect($user_entries)->each(function ($entry) use (&$user_entries_mapped) {
            $section_id = $entry->formField->formSection->id;
            if (!isset($user_entries_mapped[$section_id])) {
                $user_entries_mapped[$section_id] = [];
            }
            // TODO: Decrypt/placeholder secure fields based on Auth user permissions.
            $user_entries_mapped[$section_id][$entry->form_field_id][$entry->sort] = $entry->value;
        });

        $form_pages = FormPage::with('formSections.formFields')->orderBy('sort')->get();
        if (!$form_pages) {
            Log::error("REVIEW ERROR: Unable to fetch form pages.");
            abort(404);
        }

        $allPages = [];

        foreach ($form_pages as $page) {

            $pageContent = [
                'id'        => $page->id,
                'label'     => $page->label,
                'sections'  => [],
            ];

            $page->load(['formSections' => function($query) use ($user) {
                $query
                    ->where(function($query) use ($user) {
                        $query->where('active_start', '<=', $user->created_at)
                            ->orWhereNull('active_start');
                    })
                    ->where(function($query) use ($user) {
                        $query->where('active_end', '>=', $user->created_at)
                        ->orWhereNull('active_end');
                    })
                    ->with('formFields');
            },'formSections.formConditions']);

            $sections = $page->formSections;

            foreach ($sections as $section) {

                // if (!isset($user_entries_mapped[$section->id])) {
                //     continue;
                // }

                $pageSection = [
                    'id'        => $section->id,
                    'label'     => $section->label,
                    'fields'    => [],
                ];

                $nRepeats = 0;

                foreach ($section->formFields as $field) {

                    if (!isset($user_entries_mapped[$section->id][$field->id])) {
                        continue;
                    }

                    $pageFields = [];

                    foreach ($user_entries_mapped[$section->id][$field->id] as $sort => $value) {
                        if(empty($pageFields[$field->id])) {
                            $pageFields[$field->id] = [
                                'id'        => $field->id,
                                'label'     => $field->label,
                                'type'      => $field->type,
                                'value'     => []
                            ];
                        }

                        if($field->type == FormField::TYPE_UPLOAD) {
                            // check for multiple files
                            $uploads = UserUpload::where([['form_field_id', $field->id],['user_id' , $user->id]])->get();
                            foreach ($uploads as $upload){
                                $pageFields[$field->id]['value'][$sort][] = URL::to('/') . Storage::url("uploads/files/{$id}/{$upload->file_name}");
                            }
                        }
                        else {
                            // $pageFields[$field->id]['value'][$sort] = ($field->isSecure() && $user->hasRole([User::ROLE_TYPE_AGENCY_OWNER]) ? ($reviewer->can('view encrypted') ? self::decrypt($value) : "******") : $value);
                            $pageFields[$field->id]['value'][$sort] = (
                                $field->isSecure() 
                                ? (
                                    !$user->hasRole([User::ROLE_TYPE_AGENCY_OWNER]) 
                                    ? self::decrypt($value) 
                                    : "******"
                                ) 
                                : $value
                            );
                            // $pageFields[$field->id]['value'][$sort] = $field->isSecure() ? self::decrypt($value) : $value;
                            $pageFields[$field->id]['is_secure'] = $field->isSecure() ? true : false;
                            if($field->type == FormField::TYPE_DATE && !empty($pageFields[$field->id]['value'][$sort])) {
                                $pageFields[$field->id]['value'][$sort] = date('m/d/Y', strtotime($pageFields[$field->id]['value'][$sort]));
                            }
                        }
                        if($sort+1 > $nRepeats)
                            $nRepeats = $sort + 1;
                    } // end sorted value loop

                    if (sizeof($pageFields) > 0) {
                        $pageSection['fields'] = array_merge($pageSection['fields'], $pageFields);
                    }
                } // end field loop

                $pageSection['repeats'] = $nRepeats;
                $pageSection['conditions'] = $section->conditions;

                if (sizeof($pageSection['fields']) > 0) {
                    $pageContent['sections'][] = $pageSection;
                }
            } // end section loop

            if (sizeof($pageContent['sections']) > 0) {
                $allPages[] = $pageContent;
            }
        } // end page loop
        
        $aoCarriers = null;
        if (config('app.tenant') == 'Q2B' && $reviewer->hasRole([User::ROLE_TYPE_STAFF, User::ROLE_TYPE_SUPER_ADMIN])) {
            // $aoCarriers = $this->getAgencyOwnerCarriers($user->agency_owner_code);
            // dynamically retrieve agency owner code from user
            $aoCarriers = $this->getAgencyOwnerCarriers($user->agencyOwner()->AgentCode);
        }

        return response()->json([
            'id' => $user->id,
            'name' => $user->name,
            'status' => $user->status->slug,
            'updated_at' => $user->lastUpdatedAt(),
            'submitted_at' => $user->lastSubmittedAt(),
            'assigned_at' => $user->assignedAt(),
            'pages' => $allPages,
            'notes' => $user->notes()->with(['reviewer', 'formSection.formPage'])->get()->toArray(),
            'approved_by_ao' => $user->approved_by_ao,
            'direct_upline' => $user->uplineAgent ? $user->uplineAgent->AgentName : '',
            'agency_owner' => $user->agencyOwner() ? $user->agencyOwner()->AgentName : '',
            'agency_owner_code' => $user->agency_owner_code,
            'contract_level' => $user->contract_level,
            'comments' => $user->comments,
            'source' => $user->source,
            'internal_notes' => $user->getMeta('internal_notes', []),
            'blacklist' => $user->getMeta('blacklist', false),
            'carriers' => $user->getMeta('carriers', ''),
            'invitation' => UserInvite::with('group')->find($user->user_invite_id),
            'vector' => $user->getMeta('vector', []),
            'previous_experience' => FormBuilder::fetchUserEntry($user, FormLookup::Q2A_PREVIOUS_EXPERIENCE),
            'advanced_market_enrollment' => $user->getMeta('advanced_market_enrollment', null),
            'ao_carriers' => $aoCarriers,
            'pathway' => $user->pathway,
        ]);
    }

    /*
     * get tracking information of application: required in tracking
     *
     * @param $id userID of Recruit
     * @return Illumniate\Facades\Response
     */
    public function showTrack($id)
    {
        // TODO: Search informations with form-lookup
        // DEBT: We display series of form fields now
        $reviewer = Auth::user();

        if (!$user = User::find($id)) {
            abort(404);
        }

        if(!$user->status) {
            abort(404);
        }

        $approved_by = DB::select("
            SELECT u.email, u.name
            FROM user_status_history ush
            JOIN users u on u.id = ush.trigger_id
            JOIN user_statuses us on us.id = ush.user_status_id
            WHERE ush.user_id = ?
            AND us.slug = 'ho-approved'
            ", [$user->id]);

        $user_entries = UserEntry::where('user_id', $user->id)->with('formField.formSection')->orderBy('created_at', 'asc')->get();
        if (!$user_entries) {
            Log::error("REVIEW ERROR: Could not get application entries for user {$user->id}.");
            abort(404);
        }

        $user_entries_mapped = [];
        collect($user_entries)->each(function ($entry) use (&$user_entries_mapped) {
            $section_id = $entry->formField->formSection->id;
            if (!isset($user_entries_mapped[$section_id])) {
                $user_entries_mapped[$section_id] = [];
            }
            // TODO: Decrypt/placeholder secure fields based on Auth user permissions.
            $user_entries_mapped[$section_id][$entry->form_field_id][$entry->sort] = $entry->value;
        });

        $form_pages = FormPage::with('formSections.formFields')->orderBy('sort')->get();
        if (!$form_pages) {
            Log::error("REVIEW ERROR: Unable to fetch form pages.");
            abort(404);
        }

        $allPages = [];

        foreach ($form_pages as $page) {

            $pageContent = [
                'id'        => $page->id,
                'label'     => $page->label,
                'sections'  => [],
            ];

            $page->load(['formSections' => function($query) use ($user) {
                $query
                    ->where(function($query) use ($user) {
                        $query->where('active_start', '<=', $user->created_at)
                            ->orWhereNull('active_start');
                    })
                    ->where(function($query) use ($user) {
                        $query->where('active_end', '>=', $user->created_at)
                        ->orWhereNull('active_end');
                    })
                    ->with('formFields');
            },'formSections.formConditions']);

            $sections = $page->formSections;

            foreach ($sections as $section) {

                if (!isset($user_entries_mapped[$section->id])) {
                    continue;
                }

                $pageSection = [
                    'id'        => $section->id,
                    'label'     => $section->label,
                    'fields'    => [],
                ];

                $nRepeats = 0;

                foreach ($section->formFields as $field) {

                    if (!isset($user_entries_mapped[$section->id][$field->id])) {
                        continue;
                    }

                    $pageFields = [];
                    $nRepeats = 0;

                    foreach ($user_entries_mapped[$section->id][$field->id] as $sort => $value) {
                        if(empty($pageFields[$field->id])) {
                            $pageFields[$field->id] = [
                                'id'        => $field->id,
                                'label'     => $field->label,
                                'type'      => $field->type,
                                'value'     => []
                            ];
                        }

                        if($field->type == FormField::TYPE_UPLOAD) {
                            // check for multiple files
                            $uploads = UserUpload::where([['form_field_id', $field->id],['user_id' , $user->id]])->get();
                            foreach ($uploads as $upload){
                                $pageFields[$field->id]['value'][$sort][] = URL::to('/') . Storage::url("uploads/files/{$id}/{$upload->file_name}");
                            }
                        }
                        else {
                            // $pageFields[$field->id]['value'][$sort] = ($field->isSecure() ? ($reviewer->can('view encrypted') ? self::decrypt($value) : "******") : $value);
                            $pageFields[$field->id]['value'][$sort] = $field->isSecure() ? self::decrypt($value) : $value;
                            $pageFields[$field->id]['is_secure'] = $field->isSecure() ? true : false;
                            if($field->type == FormField::TYPE_DATE && !empty($pageFields[$field->id]['value'][$sort])) {
                                $pageFields[$field->id]['value'][$sort] = date('m/d/Y', strtotime($pageFields[$field->id]['value'][$sort]));
                            }
                        }

                        if($sort+1 > $nRepeats)
                            $nRepeats = $sort + 1;
                    } // end sorted value loop

                    if (sizeof($pageFields) > 0) {
                        $pageSection['fields'] = array_merge($pageSection['fields'], $pageFields);
                        $pageSection['repeats'] = $nRepeats;
                    }
                } // end field loop

                $pageSection['repeats'] = $nRepeats;
                $pageSection['conditions'] = $section->conditions;

                if (sizeof($pageSection['fields']) > 0) {
                    $pageContent['sections'][] = $pageSection;
                }
            } // end section loop

            if (sizeof($pageContent['sections']) > 0) {
                $allPages[] = $pageContent;
            }
        } // end page loop

        $ao = $user->agencyOwner();

        return response()->json([
            'id' => $user->id,
            'name' => $user->name,
            'agency_owner' => $ao ? $ao->AgentName : '',
            'direct_upline' => $user->uplineAgent ? $user->uplineAgent->AgentName : '',
            'contract_level' => $user->contract_level,
            'commissions' => '',
            'status' => $user->status->slug,
            'status_label' => $user->status->formPage ? ('Editing: ' . $user->status->name) : $user->status->name,
            'updated_at' => $user->lastUpdatedAt(),
            'submitted_at' => $user->lastSubmittedAt(),
            'assigned_at' => $user->assignedAt(),
            'pages' => $allPages,
            'comments' => $user->comments,
            'is_fully_licensed' => FormBuilder::fetchUserEntry($user, FormLookup::LICENSE_IS_LICENSED_BOOL) == FormField::SELECT_BOOL_YES,
            'approved_by' => $approved_by,
            'source' => $user->source,
            'internal_notes' => $user->getMeta('internal_notes', []),
            'blacklist' => $user->getMeta('blacklist', false),
            'carriers' => $user->getMeta('carriers', ''),
            'invitation' => UserInvite::with('group')->find($user->user_invite_id),
            'flagged' => $user->flagged ?? null
        ]);
    }

    /*
     * update status of application
     * invoked by AO/HO/Recruit
     *   editing : this is from AO/HO for recruit to require a revision (note can be attached from reviewer)
     *   submitted
     *   approved
     *   rejected ( note for reason is provided )
     *   created-unlicensed-account
     *   assigned-to-onboarding-dept
     *   unenrolled
     *   enrolled
     */
    public function updateStatus(Request $request)
    {
        $user = $request->user();

        $status = $request->status ?? '';
        $note = $request->note ?? '';

        if (!$recruit = User::find($request->id ?? '')) {
            abort(404);
        }
        $submitt_ho = UserStatus::where('slug', User::ONBOARD_STATUS_SUBMITTED_HO)->first();

        // get the count of how many times the application has been submitted to the home office by the recruit
        $ho_submitt_count = UserStatusHistory::where('user_id', $recruit->id)
                                                ->where('user_status_id', $submitt_ho->id)
                                                ->count();

        // no need to send back to AO if the home office has
        // already seen the app OR IF is PRINCIPAL app
        if(($recruit->type == 'b2b-principal' && $status === User::ONBOARD_STATUS_SUBMITTED_AO) || isset($ho_submitt_count) && $ho_submitt_count > 0 && $status === User::ONBOARD_STATUS_SUBMITTED_AO){
            $recruit->update([
                'approved_by_ao' => 1
            ]);
            // $recruit->refresh();

            $status = User::ONBOARD_STATUS_SUBMITTED_HO;
        }

        // get the previous status of the application
        $previous_status = isset($recruit->status) ? $recruit->status->slug : null;

        if($status === User::ONBOARD_STATUS_SUBMITTED_AO || $status === User::ONBOARD_STATUS_SUBMITTED_HO) {

            // Only recruit can submit application
            if (!$user->can('fill applications')) {
                abort(403, 'You aren\'t allowed to submit applications.');
            }

            // Check recruit's license (if not a b2b-principal or super admin)
            $isFullyLicensed = FormBuilder::fetchUserEntry($recruit, FormLookup::LICENSE_IS_LICENSED_BOOL) == FormField::SELECT_BOOL_YES;
            $hasPassedExam = FormBuilder::fetchUserEntry($recruit, FormLookup::STATE_EXAM_STATUS) == FormField::SELECT_BOOL_YES;
            if($recruit->type != 'b2b-principal' && $recruit->type != 'b2b-agent' && !$user->hasRole([User::ROLE_TYPE_STAFF, User::ROLE_TYPE_SUPER_ADMIN]) && !$isFullyLicensed && !$hasPassedExam) {
                abort(403, 'Sorry, you don\'t have valid insurance license. Please review your information and submit again.');
            }

            // Check sign status when application is submitted
            if(!App::environment(['local','staging']) && !$recruit->isSignatureSigned()) {
                abort(403, 'Sorry, you haven\'t signed agreements yet.');
            }

            // Update pathway fields
            if (!str_contains($recruit->type, 'b2b')) {
                $this->updatePathwayFields($recruit);
            }


        } else {
            if (!$user->can('review applications') && !in_array($status, [User::ONBOARD_STATUS_ENROLLED, User::ONBOARD_STATUS_UNENROLLED]))
                abort(403, 'User doesn\'t have permission to review applications.');
        }

        $thisTask = null;
        $recruitStatusProvider = (new Agents())->for($recruit);

        $isToEdit = !in_array($status, [
            User::ONBOARD_STATUS_SUBMITTED_AO,
            User::ONBOARD_STATUS_SUBMITTED_HO,
            User::ONBOARD_STATUS_AO_APPROVED,
            User::ONBOARD_STATUS_HO_APPROVED,
            User::ONBOARD_STATUS_REJECTED_AO,
            User::ONBOARD_STATUS_REJECTED_HO,
            User::ONBOARD_STATUS_UNLICENSED,
            User::ONBOARD_STATUS_ASSIGNED,
            User::ONBOARD_STATUS_UNENROLLED,
            User::ONBOARD_STATUS_ENROLLED,
            User::ONBOARD_STATUS_EXISTS_HQ,
            User::ONBOARD_STATUS_EXISTS_B2BHQ
        ]);


        // if this is a q2b agent and the service level is advisory, we need to skip the home office approval step
        if(
            config('app.tenant') == 'Q2B' 
            && $status == User::ONBOARD_STATUS_AO_APPROVED 
            && $recruit->service_level == User::SERVICE_LEVEL_ADVISORY
        ) {
            $recruit->update([
                'approved_by_ao' => $ho_submitt_count ? 1 : 0
            ]);
            $status = User::ONBOARD_STATUS_HO_APPROVED;
        }

        if ($isToEdit) { // back to editing for revision
            $lastVisitedFormStatus = $recruit->getLastVisitedFormStatus();


            //add a status history record indicating the request for revisions
            $revision_status_slug = $user->hasRole(User::ROLE_TYPE_AGENCY_OWNER) ? User::ONBOARD_STATUS_REVISION_AO : User::ONBOARD_STATUS_REVISION_HO;
            $recruitStatusProvider->setStatus($revision_status_slug, $user->id, $note);

            if($lastVisitedFormStatus->userStatus->formPage) {
                $recruitStatusProvider->setStatus($lastVisitedFormStatus->userStatus->formPage->step_ident, $user->id);
            }
            $recruit->update([
                'approved_by_ao' => $ho_submitt_count ? 1 : 0
            ]);

            $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->target($recruit->id)->find();

            RevisionRequested::dispatch($recruit, $note);

            // Disabling per ONBST2-507
            // AO nofification that their downline recruit needs to make revisions requested by the home office
            // if(isset($recruit->upline_agent_id) && $status == User::ONBOARD_STATUS_REVISION_HO)
            // {
            //     $ao = Agent::find($recruit->agency_owner_code);
            //     Mail::to($ao->AgentEmail)->send(new SendRevisionRequestedEmail($recruit->name, $ao->AgentName, $note));
            // }
        }
        //need to check the agency owner's service level
        else if (!$recruit->approved_by_ao) {

            if ($status == User::ONBOARD_STATUS_SUBMITTED_AO) {
                // Complete any existing tasks for the recruit first
                UserTask::where('user_id', $recruit->id)
                    ->whereNot('completed')
                    ->where(function($query) {
                        $query->where('type', UserTask::TYPE_FILL_APPLICATION)
                              ->orWhere('type', UserTask::TYPE_UPDATE_APPLICATION);
                    })
                    ->get()
                    ->each(function($task) {
                        UserTasks::complete($task);
                    });

                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_SUBMITTED_AO, $user->id);

                $ao = null;
                $aoRecruit = $recruit->agencyOwner();
                if($aoRecruit) {
                    $ao = User::where('agent_code', $aoRecruit->AgentCode)->first();
                }

                if(!$ao) {
                    abort(404, 'Candidate doesn\'t have a valid agency owner. Please make sure this candidate is invited from a verified user.');
                }

                // Make sure the AO has a task to approve the application
                $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->for($ao)->target($recruit->id)->find();
                if (!$thisTask)
                    $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->for($ao)->target($recruit->id)->save();

                AOSubmitted::dispatch($recruit);
            } else {

                $agent = null;
                $aoRecruit = $recruit->agencyOwner();
                if($aoRecruit)
                {
                    $agent = User::where('agent_code', $aoRecruit->AgentCode)->first();
                }

                if(!$agent) {
                    abort(404, 'Candidate doesn\'t have a valid agency owner. Please make sure this candidate is invited from a verified user.');
                }

                if ($status == User::ONBOARD_STATUS_AO_APPROVED) {

                    $recruit->setMeta('advanced_market_enrollment', $request->advanced_market_enrollment ?? null);

                    if (!$user->can('approve applications'))
                        abort(403, 'User doesn\'t have permission to approve applications.');

                    $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_AO_APPROVED, $user->id, $note);
                    $recruit->update([
                        'approved_by_ao' => 1,
                    ]);
                    
                    $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_SUBMITTED_HO, $user->id);

                    $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->for($agent)->target($recruit->id)->find();

                    AOApproved::dispatch($recruit);
                    
                    if(isset($request->carriers) && is_array($request->carriers))
                        $recruit->setMeta('carriers', implode(", ", $request->carriers));
                } else if ($status == User::ONBOARD_STATUS_REJECTED_AO) {
                    if (!$user->can('reject applications'))
                        abort(403);

                    AORejected::dispatch($recruit, $note);
                    $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_REJECTED_AO, $user->id, $note);

                    $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->for($agent)->target($recruit->id)->find();
                } elseif ( $status == User::ONBOARD_STATUS_UNENROLLED) {
                    if($previous_status !== $status) {
                        Unenrolled::dispatch($recruit, $aoRecruit);
                        $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_UNENROLLED, $user->id, $note);
                        // $thisTask = UserTasks::create(UserTask::TYPE_UNENROLLED_CONTACT_AO)->for($recruit)->target($recruit->id)->find();
                    }

                } elseif ( $status == User::ONBOARD_STATUS_ENROLLED) {
                    if($previous_status !== $status) {
                        Enrolled::dispatch($recruit, $aoRecruit);
                        $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_ENROLLED, $user->id, $note);
                        // $thisTask = UserTasks::create(UserTask::TYPE_ENROLLED_EXAM)->for($agent)->target($recruit->id)->find();
                    }
                }

            }
        } else {

            if ($status == User::ONBOARD_STATUS_SUBMITTED_HO) {
                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_SUBMITTED_HO, $user->id);

                $thisTask = UserTasks::create(UserTask::TYPE_FILL_APPLICATION)->for($recruit)->target($recruit->id)->find();
                if (!$thisTask)
                    $thisTask = UserTasks::create(UserTask::TYPE_UPDATE_APPLICATION)->for($recruit)->target($recruit->id)->save();

                HOSubmitted::dispatch($recruit);
            } else if ($status == User::ONBOARD_STATUS_HO_APPROVED) {
		        Log::info('HO_APPROVED: '.$recruit->id.' '.config('app.env').' '.config('app.tenant'));
                if (!$user->can('final approve applications') && $recruit->service_level != User::SERVICE_LEVEL_ADVISORY)
                    abort(403, 'User doesn\'t have permission to approve applications.');

                $approval_date = $recruit->getHomeOfficeApprovalDate();
                if($approval_date != null && $recruit->status->slug == User::ONBOARD_STATUS_HO_APPROVED && $note != 'override')
                    abort(403, 'This application has already been approved.');

                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_HO_APPROVED, $user->id);

                $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->target($recruit->id)->find();

                // Push to SureLC if production and Q2B
                if(config('app.env') == 'production' && config('app.tenant') == 'Q2B') {
                    Log::info('Pushing B2B user to SureLC '.$recruit->id);
                    $sureLcService = resolve('App\Services\SureLcService');
                    if($recruit->type == 'b2b-principal') {
                        $ssn = FormBuilder::fetchUserEntry($recruit, FormLookup::PRINCIPAL_SSN);
                        $ssn = str_replace('-', '', $ssn);
                        $data = [
                            'lastName' => FormBuilder::fetchUserEntry($recruit, FormLookup::PRINCIPAL_LAST_NAME),
                            'email' => FormBuilder::fetchUserEntry($recruit, FormLookup::PRINCIPAL_EMAIL),
                            'ssn' => $ssn,
                            'noEmails' => false,
                        ];
                        $result =$sureLcService->pushProducer($data);
                    } else if ($recruit->type == 'b2b-agent') {
                        $ssn = FormBuilder::fetchUserEntry($recruit, FormLookup::B2B_AGENT_SSN);
                        $ssn = str_replace('-', '', $ssn);

                        $agencyOwner = $recruit->agencyOwner();
                        $data = [
                            'lastName'   => FormBuilder::fetchUserEntry($recruit, FormLookup::B2B_AGENT_LAST_NAME),
                            'email'      => FormBuilder::fetchUserEntry($recruit, FormLookup::B2B_AGENT_EMAIL),
                            'ssn'        => $ssn,
                            'noEmails'   => false,
                            'branchCode' => $agencyOwner->AgentName,
                        ];
                        $result = $sureLcService->pushProducer($data);
                    }
                    Log::info('SureLC data: '.print_r($data, true));
                    Log::info('SureLC result: '.print_r($result, true));
                }

                // Push to SureLC if production and Q2A
                if(config('app.env') == 'production' && config('app.tenant') == 'Q2A') {
                    Log::info('Pushing Q2A user to SureLC '.$recruit->id);
                    $sureLcService = resolve('App\Services\SureLcService');
                    $ssn = FormBuilder::fetchUserEntry($recruit, FormLookup::FIELD_SSN);
                    $ssn = str_replace('-', '', $ssn);

                    $agencyOwner = $recruit->agencyOwner();
                    if($npn = $agencyOwner->NPN) {
                        // look up agency owner branchCode in sureLc
                        // $branchCode = $sureLcService->getProducerBranchCodeByNPN($npn);
			            // Log::info('Branch code: '.$npn.' '.print_r($branchCode, true));
                        $data = [
                            'lastName' => FormBuilder::fetchUserEntry($recruit, FormLookup::FIELD_LAST_NAME),
                            'email' => FormBuilder::fetchUserEntry($recruit, FormLookup::FIELD_WORK_EMAIL),
                            'ssn' => $ssn,
                            'noEmails' => false, //(bool) env('SURELC_NO_EMAILS', true),
                        ];
                        
                        $branchCodeResult = $sureLcService->getAgencyOwnerBranchCode($agencyOwner);
                        if(isset($branchCodeResult['branchCode'])) {
                            $data['branchCode'] = $branchCodeResult['branchCode'];
                        } else {
                            Log::info('SureLC: No branch code found or error occurred: ' . json_encode($branchCodeResult));
                        }
                        
                        $result = $sureLcService->pushProducer($data);
                        Log::info('SureLC data: '.print_r($data, true));
                        Log::info('SureLC result: '.print_r($result, true));
                    } else {
                        Log::info('SureLC:No NPN found for agency owner '.$agencyOwner->id);
                    }
                }
                

                HOApproved::dispatch($recruit);
            } else if ($status == User::ONBOARD_STATUS_REJECTED_HO) {
                if (!$user->can('reject applications'))
                    abort(403, 'User doesn\'t have permission to reject applications.');

                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_REJECTED_HO, $user->id, $note);

                $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->target($recruit->id)->find();

                HORejected::dispatch($recruit, $note, $request->felony_knockout);


            } else if ($status == User::ONBOARD_STATUS_UNLICENSED) {
                if (!$user->can('create hqaccount'))
                    abort(403, 'User doesn\'t have permission to create a HQ account.');

                UnlicensedAccountCreated::dispatch($recruit);
                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_UNLICENSED, $user->id, $note);

                $thisTask = UserTasks::create(UserTask::TYPE_CREATE_UNLICENSED_ACCOUNT)->target($recruit->id)->find();

                HOApproved::dispatch($recruit);
            } else if ($status == User::ONBOARD_STATUS_ASSIGNED) {
                if (!$user->can('assign to onboarding dept'))
                    abort(403, 'User doesn\'t have permission to assign candidate to onboarding department.');

                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_ASSIGNED, $user->id, $note);

                $thisTask = UserTasks::create(UserTask::TYPE_ASSIGN_TO_ONBOARDING)->target($recruit->id)->find();

                AssignedToDept::dispatch($recruit);
            }
        }

        if ($thisTask && !UserTasks::complete($thisTask))
            $this->logger->warning("Cannot complete user task: " . "Task #" . $thisTask->id);

        $recruit->updateStatusFields();

        return 'ok';
    }

    /*
     * Get onboarding statuses
     *  Required in application filtering by status
     * @return Response: json array
     */
    public function getStatusOptions() {
        $user = Auth::user();
        if ($user->hasRole(User::ROLE_TYPE_STAFF) || $user->hasRole(User::ROLE_TYPE_SUPER_ADMIN)) {
            return response()->json([
                [ "label" => "-", "value" => "" ],
                [ "label" => "Submitted to AO", "value" => User::ONBOARD_STATUS_SUBMITTED_AO ],
                [ "label" => "Submitted to Home Office", "value" => User::ONBOARD_STATUS_SUBMITTED_HO ],
                [ "label" => "Approval Error", "value" => User::ONBOARD_STATUS_APPROVAL_ERROR ],
                [ "label" => "Approved by AO", "value" => User::ONBOARD_STATUS_AO_APPROVED ],
                [ "label" => "Approved by Home Office ", "value" => User::ONBOARD_STATUS_HO_APPROVED ],
                [ "label" => "Flagged - NPN Exists", "value" => User::ONBOARD_STATUS_EXISTS ],
                [ "label" => "Flagged - NPN Exists HQ", "value" => User::ONBOARD_STATUS_EXISTS_HQ ],
                [ "label" => "Flagged - NPN Exists B2B HQ", "value" => User::ONBOARD_STATUS_EXISTS_B2BHQ ],
                [ "label" => "Rejected - AO", "value" => User::ONBOARD_STATUS_REJECTED_AO ],
                [ "label" => "Rejected - Home Office", "value" => User::ONBOARD_STATUS_REJECTED_HO ],
                [ "label" => "Created Unlicensed Account", "value" => User::ONBOARD_STATUS_UNLICENSED ],
                [ "label" => "In Progress", "value" => 'in-progress'],
                [ "label" => "Stale Applications", "value" => 'stale'],
            ]);
        } else {
            //no approval error status
            return response()->json([
                [ "label" => "-", "value" => "" ],
                [ "label" => "Submitted to AO", "value" => User::ONBOARD_STATUS_SUBMITTED_AO ],
                [ "label" => "Submitted to Home Office", "value" => User::ONBOARD_STATUS_SUBMITTED_HO ],
                [ "label" => "Approved by AO", "value" => User::ONBOARD_STATUS_AO_APPROVED ],
                [ "label" => "Approved by Home Office ", "value" => User::ONBOARD_STATUS_HO_APPROVED ],
                [ "label" => "Rejected - AO", "value" => User::ONBOARD_STATUS_REJECTED_AO ],
                [ "label" => "Rejected - Home Office", "value" => User::ONBOARD_STATUS_REJECTED_HO ],
                [ "label" => "Created Unlicensed Account", "value" => User::ONBOARD_STATUS_UNLICENSED ],
                [ "label" => "In Progress", "value" => 'in-progress'],
                [ "label" => "Stale Applications", "value" => 'stale'],
            ]);
        }
    }

    /*
    * Quick function to help contracting with the backlog of documents prior to getting auto uploads working
    * This function should become obsolete once they're caught up. Called via a button click while viewing an app.
    */
    public function uploadFilesToHq($id) {
        // $reviewer = Auth::user();

        if (!$user = User::find($id)) {
            abort(404);
        }

        if(!$user->status) {
            abort(404);
        }

        if($user && !$user->agent_code) {
            $remote_account = UserRemoteAccount::where('user_id', $user->id)->first();
            if($remote_account->agent_code)
                $user->update([
                    'agent_code' => $remote_account->agent_code
                ]);
        }
        $file_count = HQAccounts::uploadFilesToHq($id);
        return $file_count." files uploaded";
    }

    public function adminResetPassword(Request $request)
    {
        $user = User::find($request->id);
        $result = $user->forceFill([
            'password' => Hash::make($request->password),
        ])->save();
        return $user;
    }

    public function saveInternalNote(Request $request) {
        $user = Auth::user();

        $app_user = User::find($request->app_id);
        $notes = $app_user->getMeta('internal_notes', []);

        array_push($notes, [
            "note" => $request['note'],
            "user_name" => $user->name,
            "timestamp" => time()
        ]);
        $app_user->setMeta('internal_notes', $notes);
        return "OK";
    }

    public function getInternalNotes(Request $request) 
    {
        $app_user = User::find($request->id);
        $notes = $app_user->getMeta('internal_notes', []);
        return $notes;
    }

    public function verifyNpn(Request $request)
    {
        Log::info("VERIFY NPN REQUEST: ".print_r($request->all(), true));
        $npnApi = resolve('App\Services\NiprSoapService');
        $user = Auth::user();

        $first_name = $request->first_name ?? FormBuilder::fetchUserEntry($user, FormLookup::FIELD_FIRST_NAME);
        $last_name = $request->last_name ?? FormBuilder::fetchUserEntry($user, FormLookup::FIELD_LAST_NAME);
        $ssn_last_4 = $request->ssn_last_4;
        $licenseId = $request->licenseId;
        $state = $request->state;

        $npn = "";

        try {
            if($request->licenseId && strlen($request->licenseId) > 1)
                $npn = $npnApi->NPNLookupWithLicenseNumber($licenseId, $state);
            else
                $npn = $npnApi->NPNLookupWithSSN($ssn_last_4, $first_name, $last_name);

            Log::info("NPN LOOKUP RESULT: ".print_r($npn, true));

            if($npn) {
                $db_name = config('database.connections.sqlsrv.database');
                //see if NPN exists in HQ/B2BHQ
                if(!$request->staff_override) {
                    $npn_exists = false;
                    // $recruitStatusProvider = (new Agents())->for($user);
                    if(Agent::where('NPN', $npn)->active()->count() > 0) {
                        // $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_SUBMITTED_HO, $user->id);
                        $user->removeMeta('npn');
                        $user->setMeta('npn-exists', 'HQ');
                        $user->setMeta('pending-npn', $npn);
                        $user->flagged = 'NPN Exists HQ';
                        $user->save();
                        $npn_exists = array('exists' => 'HQ', 'message' => 'Your NPN was identified with an approved release request. An approved release request is required.');
                    }
                    if(AgentB2B::where('NPN', $npn)->active()->count() > 0) {
                        // $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_SUBMITTED_HO, $user->id);
                        $user->removeMeta('npn');
                        $user->setMeta('npn-exists', 'B2BHQ');
                        $user->setMeta('pending-npn', $npn);
                        $user->flagged = 'NPN Exists B2BHQ';
                        $user->save();
                        $npn_exists = array('exists' => 'B2BHQ', 'message' => 'Your NPN was identified with an affiliated partner. An approved release request is required.');
                    }

                    // if we're in Q2B, we need to save the NPN to the user's meta data
                    if(config('app.tenant') == 'Q2B') {
                        $user->setMeta('npn', $npn);
                        $user->setMeta('npn_verified_on', time());
                        // return array('npn' => $npn);
                        Log::info("NPN SAVED TO USER META DATA FOR Q2B USER ".$user->id.": ".$npn);
                    } else {
                        Log::info("NPN NOT SAVED TO USER META DATA FOR Q2B USER ".$user->id.": ".$npn);
                    }

                    // if we found an npn in either HQ or B2B HQ
                    if(!!$npn_exists) {
                        //Find the field for "NPN Exists"
                        $field = FormField::where('label', 'like', 'NPN Exists')
                            ->where(function($field) use ($user) {
                                $field->where('active_start', '<=', $user->created_at)
                                    ->orWhereNull('active_start');
                            })
                            ->where(function($field) use ($user) {
                                $field->where('active_end', '>=', $user->created_at)
                                ->orWhereNull('active_end');
                            })->first();
                        //save the response for this app
                        //manually look for existing record
                        if($field) {
                            $entry = UserEntry::where('user_id', $user->id)
                                ->where('form_field_id', $field->id)
                                ->where('sort', 0);
                            
                            if($entry->count() == 1) {
                                // we'll update the existing entry
                                $item = $entry->first();
                            } elseif($entry->count() > 1) {
                                //if there's more than one, delete them all and add a new one - this was happening periodically for some reason
                                $entry->delete();
                                $item = new UserEntry();
                            } else {
                                $item = new UserEntry();
                            }
                            $item->user_id = $user->id;
                            $item->form_field_id = $field->id;
                            $item->sort = 0;
                            $item->value = 'YES';
                            $item->save();
                        }
                        $user->setMeta('npn', $npn);
                        $user->setMeta('npn_verified_on', time());

                        return $npn_exists;
                    }
                }
                $user->setMeta('npn', $npn);
                $user->setMeta('npn_verified_on', time());
                return array('npn' => $npn);
            }
            return array('message' => 'No entity found');
        } catch (\Exception $e) {
            $message = $e->getMessage() . "\n" . $e->getTraceAsString();
            return $message;
        }

        return array('message' => 'No entity found');
    }

    public function toggleBlacklist(Request $request)
    {

        if($user = User::find($request->id)){
            $blacklist = $user->getMeta('blacklist', false);
            if (!$blacklist) {
                // Update the email address to "username_blocked@domain"
                $emailParts = explode('@', $user->email);
                // Ensure '_blocked' is added only if it isn't already
                if (!str_contains($emailParts[0], '_blocked')) {
                    $user->email = $emailParts[0] . '_blocked@' . $emailParts[1];
                }
                // Update the password to the default "Blocked123!@#"
                $user->password = bcrypt('Blocked123!@#'); // Use bcrypt to hash the password

            }else{
                // Removing from the blacklist
                $emailParts = explode('@', $user->email);

                // Remove '_blocked' from the email if it exists
                if (str_contains($emailParts[0], '_blocked')) {
                    $emailParts[0] = str_replace('_blocked', '', $emailParts[0]);
                    $user->email = $emailParts[0] . '@' . $emailParts[1];
                }

                $user->password = bcrypt(Str::random(12));
            }
            // Save the changes to the user
            $user->save();
            if (!$blacklist) {
                // Update the email address to "username_blocked@domain"
                $emailParts = explode('@', $user->email);
                // Ensure '_blocked' is added only if it isn't already
                if (!str_contains($emailParts[0], '_blocked')) {
                    $user->email = $emailParts[0] . '_blocked@' . $emailParts[1];
                }
                // Update the password to the default "Blocked123!@#"
                $user->password = bcrypt('Blocked123!@#'); // Use bcrypt to hash the password

            }else{
                // Removing from the blacklist
                $emailParts = explode('@', $user->email);

                // Remove '_blocked' from the email if it exists
                if (str_contains($emailParts[0], '_blocked')) {
                    $emailParts[0] = str_replace('_blocked', '', $emailParts[0]);
                    $user->email = $emailParts[0] . '@' . $emailParts[1];
                }

                $user->password = bcrypt(Str::random(12));
            }
            // Save the changes to the user
            $user->save();
            $user->setMeta('blacklist', !$blacklist);
            return ['blacklist' => !$blacklist];
        }
        return ['error' => 'Failed to updated'];
        
    }

    public function sendReminder(Request $request)
    {
        if(!$user = User::find($request->id))
            return ['error' => 'User not found.'];

        if($user->reminders >= 2)
            return ['error' => 'Reminder limit has been reached.'];

        if($user->last_reminder_date) {

            $last_reminder_date = substr($user->last_reminder_date, 0, strrpos($user->last_reminder_date, ' '));

            $end = Carbon::parse($last_reminder_date);
            $now = Carbon::now();
            $length = $end->diffInDays($now);
            if($length < 7)
                return ['error' => 'Last send date is less than 7 days.'];
        }

        $user->reminders = $user->reminders > 0 ? $user->reminders + 1 : 1;
        $user->last_reminder_date = Carbon::now();
        // temp disable updating of timestamps, we want to keep the updated_at field as-is
        $user->timestamps = false;
        $user->save();

        Mail::to($user->email)->send(new SendReminderEmail($user->name));
        return ['success' => true];
    }

    function clearNpnFlag(Request $request)
    {
        if(!$user = User::find($request->id))
            return ['error' => 'User not found.'];

        $status = UserStatus::where('slug', 'license-information')->first();

        $recruitStatusProvider = (new Agents())->for($user);
        $recruitStatusProvider->setStatus($status->id, $user->id);
        $npn = $user->getMeta('pending-npn', '');
        $user->setMeta('npn', $npn);
        $user->setMeta('pending-npn', $npn);
        $user->flagged = null;
        $user->user_status_id = $status->id;
        $user->save();
        return ['success' => true];
    }

    function rejectApplicationNpn(Request $request)
    {
        // put app into rejected status
        // send email
        $user = $request->user();
        if (!$recruit = User::find($request->id ?? '')) {
            abort(404);
        }
        $recruitStatusProvider = (new Agents())->for($recruit);

        if (!$user->can('reject applications'))
            abort(403, 'User doesn\'t have permission to reject applications.');

        $note = "NPN Rejected";
        
        $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_REJECTED_HO, $user->id, $note);

        // $thisTask = UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->target($recruit->id)->find();


        $felony_knockout = false;
        $npn_rejected = true;
        HORejected::dispatch($recruit, $note, $felony_knockout, $npn_rejected);
    }

    public function getVectorOneData(Request $request)
    {
        $user = $request->user();
        if (!$recruit = User::find($request->id ?? '')) {
            abort(404);
        }
 
        $ssn = null;
        $ein = null;
        $state = null;

        if(config('app.tenant') == 'Q2B') {
            switch($recruit->type) {
                case 'b2b-principal':
                    $ssn = FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_SSN);
                    $ein = FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_EIN);
                    $state = FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_STATE);
                    $first_name = FormBuilder::fetchUserEntry($recruit, FormLookup::PRINCIPAL_FIRST_NAME);
                    $last_name = FormBuilder::fetchUserEntry($recruit, FormLookup::PRINCIPAL_LAST_NAME);
                    break;
                case 'b2b-agent':
                    $ssn = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_SSN);
                    $ein = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_EIN);
                    $state = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_STATE);
                    $first_name = FormBuilder::fetchUserEntry($recruit, FormLookup::B2B_AGENT_FIRST_NAME);
                    $last_name = FormBuilder::fetchUserEntry($recruit, FormLookup::B2B_AGENT_LAST_NAME);
                    break;
                default:
                    // $ssn = FormBuilder::fetchUserEntry($user, FormLookup::FIELD_SSN);
                    // $state = FormBuilder::fetchUserEntry($user, FormLookup::FIELD_STATE);
                    break;
            }

            $ssn = str_replace('-', '', $ssn);
            $ein = str_replace('-', '', $ein);

            if(!$ssn && !$ein) {
                abort(404, 'SSN or EIN not found');
            }

            if(!$state) {
                abort(404, 'State not found');
            }
            // enable this code locally to test with mock data if IP is not whitelisted
            if(in_array($ssn, ['*********', '518111111', '126348176'])) {
                $response = json_decode($this->mockVector($ssn));
            } else {
                $vectorService = resolve('App\Services\VectorService');
                $response = $vectorService->searchBySSN($ssn, $state);
            }

            $user->setMeta('vector', $response);
        } else {
            $response = [];
        }
            
        

        // lookup npn
        $ssn_last_4 = substr($ssn, -4);
        
        
        // if(in_array($ssn, ['*********', '518111111', '126348176'])) {
        //     $ssn_last_4 = '9843';
        //     $first_name = 'Chad';
        //     $last_name = 'Wyatt';
        // }

        // TODO: refactor verifyNpn
        $request->ssn_last_4 = $ssn_last_4;
        $request->first_name = $first_name;
        $request->last_name = $last_name;

        $npn = $this->verifyNpn($request);

        // $npn['message'] = 'Your NPN was identified with an affiliated partner. An approved release request is required.';
        // $npn['exists'] = 'B2B';

        $response = ['vector' => $response, 'npn' => $npn];
        return response()->json($response);
    }

    private function mockVector($ssn)
    {
        $mock['518111111'] = '{
            "EntitySearchResult": {
                "DateTime": "9\/3\/2024 5:04:16 PM",
                "Entities": {
                    "EntityReports.EntitySearchResult": {
                        "AgencyName": "",
                        "Disputes": "1",
                        "EntityID": "518111111",
                        "EntityIDType": "SSN",
                        "EntityType": "AGT",
                        "ErrorCode": "0",
                        "ErrorDescription": "Success",
                        "FirstName": "BRIAN",
                        "LastName": "BROKER",
                        "Matches": "2",
                        "MiddleInitial": "",
                        "ReferenceID": "ref01",
                        "Reports": {
                            "EntityReports.Report": {
                                "Address1": "123 Insurance St.",
                                "Address2": "Attn: Licensing & Contracting",
                                "City": "Anytown",
                                "Contact": "Contact Person",
                                "EmailAddress": "<EMAIL>",
                                "Fax": "************",
                                "ReportingCompany": "Sample Company",
                                "ReportingOffice": "Sample Company",
                                "State": "AZ",
                                "Telephone": "************",
                                "Zip": "85260"
                            }
                        },
                        "State": "FL"
                    }
                },
                "ErrorCode": "0",
                "ErrorDescription": "Success",
                "ReferenceID": "inquiry83432"
            }
        }';
        
        $mock['*********'] = '{
            "EntitySearchResult": {
                "DateTime": "9\/3\/2024 5:02:02 PM",
                "Entities": {
                    "EntityReports.EntitySearchResult": {
                        "AgencyName": null,
                        "Disputes": null,
                        "EntityID": "*********",
                        "EntityIDType": "SSN",
                        "EntityType": null,
                        "ErrorCode": "0",
                        "ErrorDescription": "Success",
                        "FirstName": null,
                        "LastName": null,
                        "Matches": "0",
                        "MiddleInitial": null,
                        "ReferenceID": "ref01",
                        "Reports": null,
                        "State": "FL"
                    }
                },
                "ErrorCode": "0",
                "ErrorDescription": "Success",
                "ReferenceID": "inquiry83432"
            }
        }';

        $mock['126348176'] = '{
            "EntitySearchResult": {
                "DateTime": "9\/3\/2024 3:02:36 PM",
                "Entities": {
                    "EntityReports.EntitySearchResult": {
                        "AgencyName": "",
                        "Disputes": "0",
                        "EntityID": "126348176",
                        "EntityIDType": "SSN",
                        "EntityType": "AGT",
                        "ErrorCode": "0",
                        "ErrorDescription": "Success",
                        "FirstName": "ANDY",
                        "LastName": "AGENT",
                        "Matches": "1",
                        "MiddleInitial": "A",
                        "ReferenceID": "ref01",
                        "Reports": {
                            "EntityReports.Report": {
                                "Address1": "123 Insurance St.",
                                "Address2": "Attn: Licensing & Contracting",
                                "City": "Anytown",
                                "Contact": "Contact Person",
                                "EmailAddress": "<EMAIL>",
                                "Fax": "************",
                                "ReportingCompany": "Sample Company",
                                "ReportingOffice": "Sample Company",
                                "State": "AZ",
                                "Telephone": "************",
                                "Zip": "85260"
                            }
                        },
                        "State": "FL"
                    }
                },
                "ErrorCode": "0",
                "ErrorDescription": "Success",
                "ReferenceID": "inquiry83432"
            }
        }';

        return $mock[$ssn];
    }

    /**
     * Generates a temporary SAS URL for the given Azure blob storage URL.
     * The SAS URL is valid for 1 hour.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getDownloadLink(Request $request)
    {
        // Example
        // $fullUrl = 'https://agentdocuments.blob.core.windows.net/dev/custom-contracts/wlhkeLQ1dUpVPo2lTDPddfJIVt8Py9MTJL2Jikr9.pdf';

        $fullUrl = $request->get('url');
        // Parse the URL to extract container and blob name
        $parsedUrl = parse_url($fullUrl);
        $path = ltrim($parsedUrl['path'], '/'); // Remove leading slash

        // Split the path to get container name and blob name
        $pathParts = explode('/', $path);
        $containerName = array_shift($pathParts); // 'dev'
        $blobName = implode('/', $pathParts); // 'custom-contracts/wlhkeLQ1dUpVPo2lTDPddfJIVt8Py9MTJL2Jikr9.pdf'

        // Get the Azure adapter instance
        $disk = Storage::disk('azure');
        $adapter = $disk->getAdapter();

        if ($adapter instanceof AzureBlobStorageAdapter) {
            // Set the expiry time for the SAS token
            $expiryTime = Carbon::now()->addMinutes(60); // 1 hour expiry

            // Generate the SAS token URL
            $sasUrl = $adapter->getTemporaryUrl($blobName, $expiryTime, ['container' => $containerName]);

            return response()->json(['url' => $sasUrl]);
        }

        return response()->json(['error' => 'Unable to generate SAS URL'], 500);
    }

    protected function getAgencyOwnerCarriers($agencyOwnerCode)
    {
        if (!$agencyOwnerCode) {
            return null;
        }

        try {
            $requestPath = config('quilityaccounts.apiPath') . 'trusted/agents/' . $agencyOwnerCode . '/config';
            $accessToken = HQAccounts::generateAccessToken(true);

            $client = new \GuzzleHttp\Client();
            $response = $client->request('GET', $requestPath, [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept' => 'application/json',
                ],
            ]);

            $configs = json_decode($response->getBody(), true);
            
            if (empty($configs) || !isset($configs['data']['AgentConfig'])) {
                return null;
            }

            foreach ($configs['data']['AgentConfig'] as $config) {
                if ($config['ConfigName'] === 'selected_carriers') {
                    return $config['ConfigValue'] ?? null;
                }
            }

            return null;

        } catch (\Exception $e) {
            \Log::error("Error fetching AO carriers from HQ: " . $e->getMessage());
            return null;
        }
    }

    private function updatePathwayFields($recruit)
    {
        $licensed = FormBuilder::fetchUserEntry($recruit, FormLookup::LICENSE_IS_LICENSED_BOOL);
        $previous_experience = FormBuilder::fetchUserEntry($recruit, FormLookup::Q2A_PREVIOUS_EXPERIENCE);
        $previous_contracted = FormBuilder::fetchUserEntry($recruit, FormLookup::Q2A_PREVIOUSLY_CONTRACTED);
        $advanced_markets = FormBuilder::fetchUserEntry($recruit, FormLookup::Q2A_ADVANCED_MARKETS);
        $active_other_contracts = FormBuilder::fetchUserEntry($recruit, FormLookup::Q2A_ACTIVE_OTHER_CONTRACTS);
        // $active_symmetry_contracts = FormBuilder::fetchUserEntry($recruit, FormLookup::Q2A_ACTIVE_SYMMETRY_CONTRACTS);

        // default to unlicensed
        $pathway = "U"; // Unlicensed
        if ($licensed == 'YES') {
            $pathway = "L"; // Licensed

            // Previous Experience
            if ($previous_experience == 'YES') {
                switch ($previous_contracted) {
                    case 'symmetry':
                        $pathway = "R"; // Returning to Symmetry
                        break;
                    case 'other':
                        $pathway = "T"; // Transferring
                        if ($active_other_contracts == 'YES') {
                            $pathway = "T"; // Transferring with Other Contracts
                        } else {
                            $pathway = "L"; // Contracts not active, use default of Licensed
                        }
                        break;
                    case 'none':
                        $pathway = "L"; // Should still be default of Licensed
                        break;
                }
            }

            // Advanced Markets
            if ($advanced_markets == 'YES') {
                $pathway .= "/AM"; // Advanced Markets designation appended
            } elseif ($advanced_markets == 'MAYBE') {
                $pathway .= "/AM?"; // Advanced Markets designation appended
            }
        }

        $recruit->pathway = $pathway;
        $recruit->save();
        return;
    }
}